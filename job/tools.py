# -*- coding: utf-8 -*-
# @Time: 2023-1-29 9:01
# @File: tools.py
# @IDE: PyCharm

import time
from lxml import etree
from multiprocessing.dummy import Pool
import pymysql
import csv
import datetime

import os
import json
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

# 获取当前文件的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 指定 chromedriver 的路径
driver_path = os.path.join(current_dir, 'chromedriver.exe')  # 确保 chromedriver 文件名是正确的
# 城市数据文件路径
city_data_path = os.path.join(current_dir, 'city_data.json')
# 导出数据的目录
export_dir = os.path.join(current_dir, 'exports')
if not os.path.exists(export_dir):
    os.makedirs(export_dir)

# city, all_page, spider_code
def lieSpider(key_word, city, all_page, export_to_csv=True):
    """
    主函数，用于启动爬虫
    :param key_word: 搜索关键词
    :param city: 城市名称
    :param all_page: 需要爬取的页数
    :param export_to_csv: 是否导出到CSV文件
    """
    # 使用get_city_code函数获取城市代码，如果找不到则使用默认值'410'（全国）
    city_code = get_city_code(city)
    if not city_code:
        print(f"未找到城市 '{city}' 的代码，使用默认值'410'（全国）")
        city_code = '410'
    else:
        print(f"获取到城市 '{city}' 的代码: {city_code}")
    
    # 生成需要爬取的URL列表
    urls_list = get_urls(key_word, all_page, city_code)
    print(f"将爬取 {len(urls_list)} 个页面，关键词: {key_word}，城市: {city}({city_code})")
    
    # 创建一个共享的列表，用于收集所有爬取到的职位信息
    all_jobs = []
    
    # 使用线程池进行多线程爬取
    pool = Pool(2)  # 适当增加线程数，但不宜过多以免被封IP
    results = pool.map(lambda url: get_pages(url, collect_jobs=True), urls_list)
    pool.close()
    pool.join()
    
    # 收集所有爬取到的职位信息
    for result in results:
        if result:
            all_jobs.extend(result)
    
    print(f"爬虫执行完成，共获取 {len(all_jobs)} 条职位信息")
    
    # 如果需要导出到CSV文件
    if export_to_csv and all_jobs:
        export_jobs_to_csv(all_jobs, key_word, city)
    
    return all_jobs


def get_urls(key_word, all_page, city_code):
    """
    生成需要爬取的URL列表
    :param key_word: 搜索关键词
    :param all_page: 需要爬取的页数
    :param city_code: 城市代码
    :return: URL列表
    """
    urls_list = []
    for page in range(1, int(all_page) + 1):
        url = f'https://www.liepin.com/zhaopin/?city={city_code}&dq={city_code}&currentPage={page}&pageSize=40&key={key_word}'
        urls_list.append(url)
    return urls_list


def get_city():
    """
    抓取城市列表及其对应的代码
    :return: 城市列表，每个元素为[城市名称, 城市代码]
    """
    # 检查是否已有缓存的城市数据
    if os.path.exists(city_data_path):
        try:
            with open(city_data_path, 'r', encoding='utf-8') as f:
                city_list = json.load(f)
                print(f"从缓存加载了 {len(city_list)} 个城市数据")
                return city_list
        except Exception as e:
            print(f"加载缓存的城市数据失败: {e}")
    
    print('开始抓取城市列表...')

    chrome_options = Options()
    # chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--window-size=1920,1080')  # 设置窗口大小，确保元素可见

    # 使用 Service 指定 chromedriver 路径
    service = Service(driver_path)
    driver = webdriver.Chrome(service=service, options=chrome_options)

    try:
        # 访问猎聘网职位搜索页面
        driver.get('https://www.liepin.com/zhaopin/?inputFrom=head_navigation&scene=init&workYearCode=0&ckId=ayvlgrooqq8e4w2b3yoae69sd91dmbq9')
        print("页面加载中...")
        time.sleep(5)  # 增加等待时间，确保页面完全加载
        
        # 创建一个包含所有城市和对应代码的列表
        all_city_list = []
        
        # 先点击"其他"按钮以展开更多城市选项
        try:
            print("尝试定位'其他'按钮...")
            
            # 尝试多种定位方式
            try:
                # 方法1：使用ID直接定位
                other_city_btn = driver.find_element('id', 'filter-option-other-city')
                print("通过ID找到'其他'按钮")
            except:
                try:
                    # 方法2：使用完整XPath定位
                    other_city_btn = driver.find_element('xpath', "//li[@class='options-item' and @id='filter-option-other-city']")
                    print("通过完整XPath找到'其他'按钮")
                except:
                    # 方法3：使用包含文本"其他"的元素定位
                    other_city_btn = driver.find_element('xpath', "//li[contains(@class, 'options-item')]//span[text()='其他']/parent::li")
                    print("通过文本内容找到'其他'按钮")
            
            # 打印按钮信息以便调试
            print(f"找到'其他'按钮: {other_city_btn.get_attribute('outerHTML')}")
            
            # 尝试多种点击方式
            try:
                # 方法1：直接点击
                other_city_btn.click()
                print("直接点击'其他'按钮")
            except:
                try:
                    # 方法2：使用JavaScript点击
                    driver.execute_script("arguments[0].click();", other_city_btn)
                    print("使用JavaScript点击'其他'按钮")
                except:
                    # 方法3：使用Actions链
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(driver)
                    actions.move_to_element(other_city_btn).click().perform()
                    print("使用Actions链点击'其他'按钮")
            
            print("点击'其他'按钮后等待...")
            time.sleep(3)  # 等待展开动画完成
            
            # 截图保存，便于调试
            driver.save_screenshot('after_click_other.png')
            print(f"截图已保存到: {os.path.abspath('after_click_other.png')}")
            
            # 检查是否成功展开省份列表
            province_elements = driver.find_elements('xpath', '//ul[contains(@class, "ant-menu")]/li')
            print(f"找到 {len(province_elements)} 个省份元素")
            
            if len(province_elements) > 0:
                # 处理省份和城市
                for province_element in province_elements:
                    try:
                        # 获取省份名称和代码
                        province_name = province_element.find_element('xpath', './/span[contains(@class, "ant-menu-text")]').text
                        province_code = province_element.get_attribute('data-code')
                        print(f"处理省份: {province_name}, 代码: {province_code}")
                        
                        # 点击省份以显示其下属城市
                        driver.execute_script("arguments[0].click();", province_element)
                        time.sleep(2)  # 增加等待时间
                        
                        # 获取该省份下所有城市
                        city_elements = driver.find_elements('xpath', '//div[contains(@class, "data-list")]/ul/li')
                        print(f"在 {province_name} 下找到 {len(city_elements)} 个城市")
                        
                        if len(city_elements) > 0:
                            # 添加省份本身
                            all_city_list.append([province_name, province_code])
                            
                            # 添加该省份下的所有城市
                            for city_element in city_elements:
                                try:
                                    city_id = city_element.get_attribute('id')
                                    city_name = city_element.text
                                    
                                    # 如果有id属性，说明是具体城市而非"全XX省"选项
                                    if city_id and city_id.startswith('code_'):
                                        city_code = city_id.replace('code_', '')
                                        all_city_list.append([city_name, city_code])
                                        print(f"添加城市: {city_name}, 代码: {city_code}")
                                except Exception as e:
                                    print(f"处理城市元素时出错: {e}")
                    except Exception as e:
                        print(f"处理省份元素时出错: {e}")
            else:
                print("未找到省份列表，可能点击'其他'按钮失败")
                
        except Exception as e:
            print(f"点击'其他'按钮或获取城市失败: {e}")
            
            # 如果上面的方法失败，尝试使用原来的方法获取一些基本城市
            print("尝试使用备用方法获取城市列表...")
            req_html = etree.HTML(driver.page_source)
            code_list = req_html.xpath('//li[@data-key="dq"]/@data-code')
            name_list = req_html.xpath('//li[@data-key="dq"]/@data-name')
            all_city_list = [[name, code] for name, code in zip(name_list, code_list)]
            print(f"使用备用方法找到 {len(all_city_list)} 个城市")
        
        print('抓取到的城市列表:', all_city_list)
        
        # 在函数结尾，成功获取数据后保存到文件
        try:
            # 保存城市列表到JSON文件
            save_city_list(all_city_list)
            print(f"城市数据已保存到: {os.path.abspath(city_data_path)}")
        except Exception as e:
            print(f"保存城市数据失败: {e}")
        
        return all_city_list
    except Exception as e:
        print('抓取城市列表失败:', e)
        return []
    finally:
        driver.quit()


def save_city_list(city_list):
    """
    将城市列表保存到JSON文件
    :param city_list: 城市列表，每个元素为[城市名称, 城市代码]
    """
    with open(city_data_path, 'w', encoding='utf-8') as f:
        json.dump(city_list, f, ensure_ascii=False, indent=4)


def load_city_list():
    """
    从JSON文件加载城市列表
    :return: 城市列表，每个元素为[城市名称, 城市代码]，如果文件不存在则返回空列表
    """
    if not os.path.exists(city_data_path):
        print(f"城市数据文件不存在: {city_data_path}")
        return []
        
    try:
        with open(city_data_path, 'r', encoding='utf-8') as f:
            city_list = json.load(f)
            print(f"成功加载了 {len(city_list)} 个城市数据")
            return city_list
    except Exception as e:
        print(f"加载城市数据失败: {e}")
        return []


def get_city_dict():
    """
    获取城市代码字典，格式为 {城市名称: 城市代码}
    处理重复城市名称的问题，优先使用省级城市代码
    :return: 城市代码字典
    """
    city_list = load_city_list()
    city_dict = {}
    
    # 首先添加所有城市
    for city_name, city_code in city_list:
        # 如果城市名称已存在且当前代码更短（通常省级城市代码更短），则更新
        if city_name in city_dict:
            # 优先使用较短的代码（通常是省级城市代码）
            if len(city_code) < len(city_dict[city_name]):
                city_dict[city_name] = city_code
        else:
            city_dict[city_name] = city_code
    
    # 添加特殊城市代码
    city_dict['全国'] = '410'  # 确保全国代码存在
    
    return city_dict


def get_city_code(city_name):
    """
    根据城市名称获取城市代码
    :param city_name: 城市名称
    :return: 城市代码，如果不存在则返回None
    """
    city_dict = get_city_dict()
    return city_dict.get(city_name)


def export_jobs_to_csv(jobs, key_word, city):
    """
    将爬取到的职位信息导出到CSV文件
    :param jobs: 职位信息列表
    :param key_word: 搜索关键词
    :param city: 城市名称
    """
    if not jobs:
        print("没有职位信息可导出")
        return
    
    # 生成文件名，包含关键词、城市和时间戳
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"{key_word}_{city}_{timestamp}.csv"
    filepath = os.path.join(export_dir, filename)
    
    try:
        with open(filepath, 'w', newline='', encoding='utf-8-sig') as f:  # 使用utf-8-sig编码，支持Excel打开
            writer = csv.writer(f)
            # 写入表头
            writer.writerow(['职位名称', '薪资待遇', '工作地点', '学历要求', '经验要求', 
                            '公司名称', '公司行业', '公司规模', '详情链接', '搜索关键词', '城市'])
            
            # 写入数据
            for job in jobs:
                writer.writerow([
                    job['name'], job['salary'], job['address'], job['education'],
                    job['experience'], job['company'], job['label'], job['scale'],
                    job['href'], job['key_word'], job['city_name']
                ])
        
        print(f"职位信息已导出到: {filepath}")
    except Exception as e:
        print(f"导出职位信息失败: {e}")


def get_pages(url, collect_jobs=False):
    """
    爬取单个页面的职位信息并存储到数据库
    :param url: 需要爬取的页面URL
    :param collect_jobs: 是否收集职位信息并返回
    :return: 如果collect_jobs为True，则返回职位信息列表，否则返回None
    """
    # 从URL中提取城市代码和关键词
    import re
    city_code_match = re.search(r'city=([^&]+)', url)
    key_word_match = re.search(r'key=([^&]+)', url)
    
    city_code = city_code_match.group(1) if city_code_match else '410'
    key_word = key_word_match.group(1) if key_word_match else ''
    
    # 获取城市名称（仅用于日志显示，不存入数据库）
    city_name = get_city_name(city_code) or '未知城市'
    
    print(f'开始爬取 {url}...')
    print(f'城市: {city_name}({city_code}), 关键词: {key_word}')

    mysql_conn = get_mysql()
    conn = mysql_conn[0]
    cur = mysql_conn[1]
    
    if not conn or not cur:
        print("数据库连接失败，无法继续爬取")
        return [] if collect_jobs else None

    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--no-sandbox')

    service = Service(driver_path)
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    # 用于收集职位信息的列表
    jobs_list = [] if collect_jobs else None

    try:
        driver.get(url)
        time.sleep(3)
        req_html = etree.HTML(driver.page_source)

        # 提取职位信息
        name = req_html.xpath('//div[@class="jsx-2387891236 ellipsis-1"]/text()')
        salary = req_html.xpath('//span[@class="jsx-2387891236 job-salary"]/text()')
        address = req_html.xpath('//span[@class="jsx-2387891236 ellipsis-1"]/text()')
        education = req_html.xpath('//div[@class="jsx-2387891236 job-labels-box"]/span[2]/text()')
        experience = req_html.xpath('//div[@class="jsx-2387891236 job-labels-box"]/span[1]/text()')
        com_name = req_html.xpath('//span[@class="jsx-2387891236 company-name ellipsis-1"]/text()')
        tag_list = req_html.xpath('//div[@class="jsx-2387891236 company-tags-box ellipsis-1"]')
        href_list = req_html.xpath('//a[@data-nick="job-detail-job-info"]/@href')

        # 处理标签信息
        label_list = []
        scale_list = []
        for tag in tag_list:
            span_list = tag.xpath('./span/text()')
            if span_list:
                label_list.append(span_list[0])
                scale_list.append(span_list[-1])
            else:
                label_list.append('')
                scale_list.append('')

        # 确保所有列表长度一致
        lists = [name, salary, address, education, experience, com_name, label_list, scale_list, href_list]
        min_length = min(len(lst) for lst in lists)
        for lst in lists:
            lst[:] = lst[:min_length]

        print(f"获取到 {min_length} 条职位信息")
        print("-" * 80)
        print("职位详细信息:")
        print("-" * 80)

        # 插入数据库
        select_sql = 'SELECT href FROM job_data'
        cur.execute(select_sql)
        href_list_mysql = [x[0] for x in cur.fetchall()]

        # 使用原始的插入语句，不添加city_code和city_name字段
        insert_sql = '''INSERT INTO job_data(name, salary, place, education, experience, company, label, scale, href, key_word) 
                         VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)'''

        inserted_count = 0
        for i in range(min_length):
            href = href_list[i].split('?')[0]
            
            # 输出每个岗位的详细信息
            print(f"岗位 {i+1}/{min_length}:")
            print(f"  职位名称: {name[i]}")
            print(f"  薪资待遇: {salary[i]}")
            print(f"  工作地点: {address[i]}")
            print(f"  学历要求: {education[i]}")
            print(f"  经验要求: {experience[i]}")
            print(f"  公司名称: {com_name[i]}")
            print(f"  公司行业: {label_list[i]}")
            print(f"  公司规模: {scale_list[i]}")
            print(f"  详情链接: {href}")
            print(f"  搜索关键词: {key_word}")
            print(f"  城市: {city_name}")
            print("-" * 50)
            
            # 如果需要收集职位信息，则添加到列表中
            if collect_jobs:
                job_info = {
                    'name': name[i],
                    'salary': salary[i],
                    'address': address[i],
                    'education': education[i],
                    'experience': experience[i],
                    'company': com_name[i],
                    'label': label_list[i],
                    'scale': scale_list[i],
                    'href': href,
                    'key_word': key_word,
                    'city_name': city_name,
                    'city_code': city_code
                }
                jobs_list.append(job_info)
            
            if href not in href_list_mysql:
                # 不包含city_code和city_name字段
                data = (name[i], salary[i], address[i], education[i], experience[i], com_name[i], 
                        label_list[i], scale_list[i], href, key_word)
                try:
                    cur.execute(insert_sql, data)
                    conn.commit()
                    inserted_count += 1
                    print(f"  状态: 已插入数据库")
                except Exception as e:
                    print(f"  状态: 插入数据库失败: {e}")
                    conn.rollback()
            else:
                print(f"  状态: 数据已存在，跳过")
            print("-" * 50)
                
        print(f"成功插入 {inserted_count} 条新职位数据")
        return jobs_list

    except Exception as e:
        print(f'爬取页面 {url} 失败: {e}')
        return [] if collect_jobs else None
    finally:
        cur.close()
        conn.close()
        driver.quit()


def get_mysql():
    """
    连接MySQL数据库
    :return: 数据库连接和游标
    """
    try:
        conn = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            passwd='123456',
            database='recommend_job',
            autocommit=True,
            charset='utf8mb4'
        )
        cur = conn.cursor()
        return conn, cur
    except Exception as e:
        print(f'连接数据库失败: {e}')
        return None, None


def get_city_name(city_code):
    """
    根据城市代码获取城市名称
    :param city_code: 城市代码
    :return: 城市名称，如果不存在则返回None
    """
    city_list = load_city_list()
    for city_name, code in city_list:
        if code == city_code:
            return city_name
    return None


def get_city_info():
    """
    获取所有城市信息，返回两个字典：
    1. 城市名称到代码的映射
    2. 城市代码到名称的映射
    :return: (name_to_code_dict, code_to_name_dict)
    """
    city_list = load_city_list()
    name_to_code = {}
    code_to_name = {}
    
    # 处理所有城市
    for city_name, city_code in city_list:
        # 名称到代码的映射（优先使用较短的代码）
        if city_name in name_to_code:
            if len(city_code) < len(name_to_code[city_name]):
                name_to_code[city_name] = city_code
        else:
            name_to_code[city_name] = city_code
        
        # 代码到名称的映射（优先使用较短的名称）
        if city_code in code_to_name:
            if len(city_name) < len(code_to_name[city_code]):
                code_to_name[city_code] = city_name
        else:
            code_to_name[city_code] = city_name
    
    # 添加特殊城市代码
    name_to_code['全国'] = '410'
    code_to_name['410'] = '全国'
    
    return name_to_code, code_to_name


if __name__ == '__main__':
    # 设置爬取参数
    keyword = 'python'  # 搜索关键词
    city_name = '广州'  # 城市名称
    pages = '1'  # 爬取页数
    
    # 获取城市代码
    code = get_city_code(city_name)
    if not code:
        print(f"未找到城市 '{city_name}' 的代码，使用默认值'410'（全国）")
        city_name = '全国'
    else:
        print(f"城市 '{city_name}' 的代码是: {code}")
    
    # 执行爬虫并导出到CSV
    print(f"开始爬取 '{keyword}' 在 '{city_name}' 的职位信息，共 {pages} 页...")
    jobs = lieSpider(keyword, city_name, pages, export_to_csv=True)
    print(f"爬取完成，共获取到 {len(jobs)} 条职位信息")