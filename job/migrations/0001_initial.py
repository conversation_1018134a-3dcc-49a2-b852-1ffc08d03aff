# Generated by Django 3.2.8 on 2024-09-19 09:18

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='JobData',
            fields=[
                ('job_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('salary', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('place', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('education', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('experience', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('company', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('label', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('scale', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('href', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('key_word', models.<PERSON>r<PERSON>ield(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'job_data',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='SpiderInfo',
            fields=[
                ('spider_id', models.AutoField(primary_key=True, serialize=False)),
                ('spider_name', models.CharField(blank=True, max_length=255, null=True)),
                ('count', models.IntegerField(blank=True, null=True)),
                ('page', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'db_table': 'spider_info',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='UserList',
            fields=[
                ('user_id', models.CharField(max_length=11, primary_key=True, serialize=False)),
                ('user_name', models.CharField(blank=True, max_length=255, null=True)),
                ('pass_word', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'db_table': 'user_list',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='UserExpect',
            fields=[
                ('expect_id', models.AutoField(primary_key=True, serialize=False)),
                ('key_word', models.CharField(blank=True, max_length=255, null=True)),
                ('place', models.CharField(blank=True, max_length=255, null=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='job.userlist')),
            ],
            options={
                'db_table': 'user_expect',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='SendList',
            fields=[
                ('send_id', models.AutoField(primary_key=True, serialize=False)),
                ('job', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='job.jobdata')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='job.userlist')),
            ],
            options={
                'db_table': 'send_list',
                'managed': True,
            },
        ),
    ]
