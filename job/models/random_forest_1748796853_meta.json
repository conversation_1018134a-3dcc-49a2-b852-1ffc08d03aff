{"model_type": "random_forest", "model_filename": "random_forest_1748796853.pkl", "created_time": "2025-06-02 00:54:13", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.42969479095969626, "r2": 0.030116683274810097, "mae_orig": 7.273354913832628, "r2_orig": 0.19391459862806393}, "max_salary": {"mae": 0.31097352243109716, "r2": 0.4440198130371221, "mae_orig": 7.575106830399616, "r2_orig": 0.3690823519769795}, "feature_importance": {"education_不限": 0.005379363080021495, "education_中专": 0.00010300813318125124, "education_博士": 0.0012922490918096303, "education_大专": 0.051115359337230076, "education_本科": 0.012533174297345056, "education_硕士": 0.005837040680111209, "experience_1-3年": 0.08501515079250467, "experience_10年以上": 0.10618416216183761, "experience_1年以下": 0.009810523204393306, "experience_3-5年": 0.010119041499616682, "experience_5-10年": 0.05700834958824579, "experience_不限": 0.009429703366615184, "experience_应届生": 0.0006450645694292863, "place_上海": 0.008500394991102098, "place_北京": 0.004950382816119225, "place_南京": 0.0006018540638138158, "place_南宁": 0.003832860983501527, "place_南昌": 0.0, "place_厦门": 0.0, "place_大连": 0.0002623811580228263, "place_天津": 0.00023560675683888786, "place_宁波": 0.0, "place_广州": 0.0007881631557408086, "place_徐州": 0.0, "place_成都": 0.00036893235674233455, "place_无锡": 0.002774822194628967, "place_昆明": 0.0, "place_杭州": 0.000890356852798978, "place_枣庄": 0.0, "place_深圳": 0.003458924499781302, "place_温州": 0.0001371201351474075, "place_聊城": 0.0, "place_苏州": 0.0007946226260660816, "place_西安": 4.608648131543628e-05, "place_重庆": 0.029130920713949023, "place_韶关": 0.0, "scale_中大型企业": 0.004099498128894252, "scale_中小企业": 0.005796296687395004, "scale_大型企业": 0.007991548325367252, "scale_小型企业": 0.04025446748637387, "scale_未知规模": 0.0034717180499469744, "scale_超大型企业": 0.011767480769669594, "key_word_": 0.000349579307649707, "key_word_AI": 1.2874999191499799e-05, "key_word_AI+大数据": 0.0, "key_word_AI+测试": 0.0, "key_word_AI+算法": 0.0, "key_word_AI+算法+测试": 0.0, "key_word_C++": 8.442091297446368e-05, "key_word_C+++UX": 0.0, "key_word_C+++产品": 0.0, "key_word_Java": 0.003626567715536955, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 4.352476999839379e-06, "key_word_Java+Python+C++": 0.00025507047983576225, "key_word_Java+Python+C+++前端": 4.492325342293193e-05, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 9.613974571146307e-07, "key_word_Java+Python+C+++前端+算法": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 2.1048738097258693e-05, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+全栈": 0.0, "key_word_Java+后端": 0.0005375938535048984, "key_word_Java+后端+AI": 0.0, "key_word_Java+后端+全栈": 0.0, "key_word_Java+大数据": 2.4291231487141384e-05, "key_word_PHP": 0.0, "key_word_PHP+后端": 0.00019313236276343772, "key_word_Python": 0.008873091086203773, "key_word_Python+C++": 6.454631934094627e-05, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 0.0, "key_word_Python+后端": 0.0011077666156463772, "key_word_Python+后端+AI": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+测试": 3.458096601955851e-05, "key_word_Python+算法": 0.0004328543658360949, "key_word_UI": 0.0008634581689135252, "key_word_UX": 0.00024440259598096213, "key_word_android": 0.0016708678775899841, "key_word_c": 0.0006540987690759659, "key_word_c语言": 0.0026182419461282308, "key_word_java": 0.0010931077093999006, "key_word_python": 0.0007876879105535855, "key_word_web": 0.0007054362068295987, "key_word_产品": 0.001514801505999281, "key_word_产品+运营": 0.0, "key_word_产品+运营+市场": 0.0, "key_word_人工智能": 0.0, "key_word_人工智能+AI": 0.0, "key_word_全栈": 0.00015439152322367967, "key_word_前端": 0.0032150047892364974, "key_word_前端+产品": 0.0, "key_word_前端+全栈": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.048303174313695775, "key_word_后端+AI": 0.0, "key_word_后端+产品": 7.659641017370697e-06, "key_word_后端+市场": 0.0, "key_word_后端+测试": 0.0, "key_word_大数据": 0.006751984894914489, "key_word_大数据+产品": 0.0002519759350088979, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.00026832390051994095, "key_word_大数据+运营": 3.238285497162566e-05, "key_word_市场": 0.00014721224752965468, "key_word_数据分析": 0.0, "key_word_数据分析+大数据": 1.826276529296349e-05, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.004248334801258312, "key_word_测试+UI": 0.00043653012951356597, "key_word_测试+产品": 1.8342043649545185e-05, "key_word_爬虫": 0.0, "key_word_物联网": 0.0028675821806687656, "key_word_算法": 0.0005161838735404101, "key_word_算法+测试": 2.3447998354697108e-05, "key_word_运营": 0.0007568109688139868, "key_word_运营+销售": 0.0, "key_word_销售": 0.0036933327379527103, "key_word_销售+市场": 0.0, "position_level_初级": 0.02001950060734548, "position_level_普通": 0.06467899222030109, "position_level_高级": 0.12511973847018, "position_popularity_bin_中": 0.0028216162731294547, "position_popularity_bin_低": 0.020115340034666463, "position_popularity_bin_极低": 0.004981604620028937, "position_popularity_bin_极高": 0.0038621292862227956, "position_popularity_bin_高": 0.0039018544259228436, "city_popularity_bin_中": 0.00856821867770513, "city_popularity_bin_低": 0.005334500975115426, "city_popularity_bin_极低": 0.04353758834231138, "city_popularity_bin_高": 0.004159210575046681, "salary_ratio": 0.0, "position_popularity": 0.03547907982697669, "city_popularity": 0.07526330025788901}}}