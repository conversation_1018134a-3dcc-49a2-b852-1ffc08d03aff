{"model_type": "random_forest", "model_filename": "random_forest_1749138888.pkl", "created_time": "2025-06-05 23:54:48", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.4444999099466752, "r2": -0.010412212041544189, "mae_orig": 7.6703653723243574, "r2_orig": 0.06369634314881256}, "max_salary": {"mae": 0.29676336387823266, "r2": 0.4758382845068413, "mae_orig": 7.150137272835423, "r2_orig": 0.3932330551924176}, "feature_importance": {"education_不限": 0.009070793129846686, "education_中专": 0.00048196658694013307, "education_博士": 0.0013506924410188023, "education_大专": 0.04696645532377398, "education_本科": 0.01776494074728646, "education_硕士": 0.008185932188801459, "experience_1-3年": 0.08703164174568989, "experience_10年以上": 0.09462674930041481, "experience_1年以下": 0.010190578896455681, "experience_3-5年": 0.01927209436632667, "experience_5-10年": 0.055749846280900635, "experience_不限": 0.017878683628139887, "experience_应届生": 0.0007405049866615526, "place_上海": 0.010485177141561433, "place_保定": 0.0, "place_北京": 0.007177578279929793, "place_南京": 0.0002055319090621123, "place_南宁": 0.00269152669184761, "place_厦门": 0.00022711609957656184, "place_商丘": 0.0, "place_大连": 0.0002047001330798439, "place_天津": 0.0003141538448779753, "place_威海": 0.0, "place_广州": 0.0017689168447691083, "place_成都": 0.00024215500489551886, "place_无锡": 0.0014806646096083879, "place_昆明": 0.0, "place_杭州": 0.0008960658814190526, "place_深圳": 0.00536574918220511, "place_苏州": 0.000293973910802876, "place_西安": 7.58064842376799e-05, "place_重庆": 0.02841761075595935, "place_长沙": 5.333625347510472e-05, "scale_中大型企业": 0.007605660336991259, "scale_中小企业": 0.01238390074535695, "scale_大型企业": 0.015597530248909877, "scale_小型企业": 0.03565162673486779, "scale_未知规模": 0.004265276254607798, "scale_超大型企业": 0.018581584807646143, "key_word_": 0.0005863755200874468, "key_word_AI": 3.632641107787164e-05, "key_word_AI+产品": 0.0, "key_word_AI+测试": 0.0, "key_word_C++": 0.000883328752911512, "key_word_C+++UX": 0.0, "key_word_Java": 0.005288134548888609, "key_word_Java+AI": 0.0, "key_word_Java+AI+算法": 0.0, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 0.0, "key_word_Java+Python+C++": 9.720369545532728e-05, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 5.3670050841570956e-05, "key_word_Java+Python+C+++前端+算法+测试": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 3.414985438528931e-05, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+Python+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+前端": 0.0, "key_word_Java+后端": 0.0008166765566064441, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 4.815293521636994e-06, "key_word_PHP": 3.766699999111957e-05, "key_word_PHP+后端": 0.00029626972605427393, "key_word_Python": 0.007734663578654884, "key_word_Python+AI": 0.0, "key_word_Python+C++": 6.553689151451235e-06, "key_word_Python+C+++AI": 0.0, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 9.728230504234502e-06, "key_word_Python+前端+后端": 0.0, "key_word_Python+后端": 0.0008572917962958408, "key_word_Python+后端+AI": 0.0, "key_word_Python+后端+大数据": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.0005602841148508411, "key_word_UI": 0.0010649286429884066, "key_word_UX": 0.0002322159002636789, "key_word_android": 0.00435815949973684, "key_word_c": 0.0007798983124861771, "key_word_c语言": 0.005342595640306355, "key_word_java": 0.002251361088550622, "key_word_python": 0.0019216635606814885, "key_word_web": 0.0013218976142184856, "key_word_产品": 0.003263490254915871, "key_word_产品+运营": 1.9261262127273564e-06, "key_word_人工智能+销售": 0.0, "key_word_全栈": 5.7050850563825234e-05, "key_word_前端": 0.004174820147691035, "key_word_前端+全栈": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.03190605569632885, "key_word_后端+AI": 0.0, "key_word_后端+产品": 1.647148395043743e-05, "key_word_后端+大数据": 7.714127407123915e-06, "key_word_后端+市场": 0.0, "key_word_后端+测试": 0.0, "key_word_大数据": 0.010244815253429459, "key_word_大数据+产品": 0.0011077971898332472, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.0001570620189111829, "key_word_大数据+运营": 0.0, "key_word_大数据+销售": 0.0, "key_word_市场": 0.0004903131635417059, "key_word_数据分析": 2.3418768871691058e-05, "key_word_数据分析+大数据": 1.7656158680424725e-05, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.007201037207194491, "key_word_测试+UI": 0.00015881929991355632, "key_word_测试+产品": 9.071846067865463e-06, "key_word_爬虫": 0.0006337107000479493, "key_word_物联网": 0.0037439101779370857, "key_word_算法": 0.0006910067861741389, "key_word_算法+测试": 0.0, "key_word_运营": 0.0013634989792530579, "key_word_运营+销售": 0.0, "key_word_销售": 0.0034298784044526427, "key_word_销售+市场": 0.0, "position_level_初级": 0.012670190851736903, "position_level_普通": 0.06622733821212745, "position_level_高级": 0.08666978709485097, "position_popularity_bin_中": 0.006608146385754229, "position_popularity_bin_低": 0.01760713330000246, "position_popularity_bin_极低": 0.00781794556251313, "position_popularity_bin_极高": 0.004680537811258121, "position_popularity_bin_高": 0.006959937829061896, "city_popularity_bin_中": 0.009402351412374109, "city_popularity_bin_低": 0.005458964879758044, "city_popularity_bin_极低": 0.04211408199411108, "city_popularity_bin_高": 0.007244930839271122, "salary_ratio": 0.0, "position_popularity": 0.04670711914270479, "city_popularity": 0.05325962918664291}}}