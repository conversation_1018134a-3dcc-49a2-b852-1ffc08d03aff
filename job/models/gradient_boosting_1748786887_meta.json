{"model_type": "gradient_boosting", "model_filename": "gradient_boosting_1748786887.pkl", "created_time": "2025-06-01 22:08:07", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "gradient_boosting", "min_salary": {"mae": 0.43517950802150274, "r2": 0.04561071831702335, "mae_orig": 7.920075984939178, "r2_orig": -0.0869340061120516}, "max_salary": {"mae": 0.2901263006476627, "r2": 0.5016097482375876, "mae_orig": 6.872977423416936, "r2_orig": 0.4856595550060081}, "feature_importance": {"education_不限": 0.01168342878834015, "education_博士": 0.007155072260022511, "education_大专": 0.04576609112328913, "education_本科": 0.011593482978661704, "education_硕士": 0.008330763276034376, "experience_1-3年": 0.09894497176328128, "experience_10年以上": 0.10077821010502935, "experience_1年以下": 0.01579026753791216, "experience_3-5年": 0.01082316928932163, "experience_5-10年": 0.0638292697665802, "experience_不限": 0.022458498855681713, "experience_应届生": 0.0027524113824819803, "place_上海": 0.007025020747816262, "place_北京": 0.00484317351529818, "place_南京": 2.503955078658352e-05, "place_合肥": 0.0, "place_商丘": 0.0, "place_大连": 4.713332376255812e-05, "place_天津": 0.000532561440551969, "place_太原": 0.0, "place_广州": 0.00011089068939418614, "place_徐州": 0.0002119748516090632, "place_成都": 5.258545699613265e-05, "place_无锡": 0.0005373136211053183, "place_昆明": 0.0, "place_杭州": 0.0007082286190244802, "place_深圳": 0.003618599795253079, "place_温州": 0.0, "place_苏州": 0.0006161924305273216, "place_西安": 0.00039008007133323007, "place_重庆": 0.025842381263615335, "place_韶关": 0.0, "scale_中大型企业": 0.008305042594927809, "scale_中小企业": 0.01174969408429948, "scale_大型企业": 0.012940067901280772, "scale_小型企业": 0.03334500026380153, "scale_未知规模": 0.005527715537677882, "scale_超大型企业": 0.017926366451301426, "key_word_": 0.0, "key_word_AI": 0.0010046560941238982, "key_word_AI+大数据": 0.0, "key_word_AI+测试": 0.0, "key_word_AI+算法": 0.0, "key_word_AI+算法+测试": 0.0, "key_word_C++": 0.0009855291070141807, "key_word_Java": 0.005261823876988334, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 0.00041549207516089384, "key_word_Java+Python+C++": 0.00045079141611847044, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 0.0, "key_word_Java+Python+C+++前端+算法": 0.0, "key_word_Java+Python+C+++后端": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 0.0, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+全栈": 0.0, "key_word_Java+后端": 0.0007224931743641271, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 1.573623388026339e-05, "key_word_PHP": 0.0, "key_word_PHP+后端": 0.00023802954302433604, "key_word_Python": 0.004981769516397221, "key_word_Python+AI": 0.0, "key_word_Python+C++": 0.00020693608574947933, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 6.268598969676526e-05, "key_word_Python+后端": 0.0034334573949233514, "key_word_Python+后端+AI": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 4.110379905482903e-05, "key_word_Python+机器学习": 0.0003149676761353665, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.0006744524645030754, "key_word_UI": 0.0006959600980505666, "key_word_UX": 0.001242310247062677, "key_word_android": 0.0036089299904093616, "key_word_c": 0.0015322548964428501, "key_word_c语言": 0.0038916916138261306, "key_word_java": 0.0013470795244574381, "key_word_python": 0.001940084654540029, "key_word_web": 0.0012577149233360327, "key_word_产品": 0.004049910050857135, "key_word_产品+运营": 0.00031464082408296163, "key_word_产品+运营+市场": 0.0, "key_word_人工智能": 0.0, "key_word_全栈": 1.9516699124518275e-05, "key_word_前端": 0.0010203694918446474, "key_word_前端+产品": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.05790105605653408, "key_word_后端+产品": 0.0, "key_word_后端+全栈": 0.0, "key_word_后端+市场": 0.0, "key_word_大数据": 0.0032294407767830976, "key_word_大数据+产品": 0.000709172158029396, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.00039419307198338674, "key_word_大数据+运营": 0.0, "key_word_大数据+销售": 0.0, "key_word_市场": 0.0011125208129786608, "key_word_数据分析": 0.00014999775776488387, "key_word_数据分析+大数据": 0.0004807560715446327, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.007533492676792435, "key_word_测试+UI": 0.004656232802835724, "key_word_测试+产品": 0.0014521046569007522, "key_word_爬虫": 0.0019393309269017263, "key_word_物联网": 0.004209839598101956, "key_word_算法": 0.001190416254577511, "key_word_算法+测试": 0.0, "key_word_运营": 0.0065582983354501275, "key_word_销售": 0.003798674469938365, "position_level_初级": 0.010091343751898582, "position_level_普通": 0.011167019924927852, "position_level_高级": 0.13862854353053472, "position_popularity_bin_中": 0.007265561791809688, "position_popularity_bin_低": 0.00661027735025612, "position_popularity_bin_极低": 0.001927639973489972, "position_popularity_bin_极高": 0.0009719562207658483, "position_popularity_bin_高": 0.003968522128356132, "city_popularity_bin_中": 0.006813728474875859, "city_popularity_bin_低": 0.005189574006256173, "city_popularity_bin_极低": 0.04843461326792331, "city_popularity_bin_高": 0.00310796027292473, "salary_ratio": 0.0, "position_popularity": 0.04513024549282197, "city_popularity": 0.041386400587874614}}}