{"model_type": "random_forest", "model_filename": "random_forest_1749138955.pkl", "created_time": "2025-06-05 23:55:55", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.4428683762309809, "r2": -0.005625516216022142, "mae_orig": 7.63028463895591, "r2_orig": 0.06930190090945365}, "max_salary": {"mae": 0.2972445354437818, "r2": 0.4741888680199937, "mae_orig": 7.154513737280581, "r2_orig": 0.3942293856174913}, "feature_importance": {"education_不限": 0.00831257527263102, "education_中专": 0.0003457818729185917, "education_博士": 0.0013137498721858846, "education_大专": 0.04392821402239486, "education_本科": 0.017129134018940215, "education_硕士": 0.007920371148869867, "experience_1-3年": 0.09080810279567673, "experience_10年以上": 0.09498506459915473, "experience_1年以下": 0.01174309574538586, "experience_3-5年": 0.019920820398361953, "experience_5-10年": 0.06282075226468985, "experience_不限": 0.017836053248169087, "experience_应届生": 0.0007272157786624799, "place_上海": 0.009449894681621373, "place_保定": 0.0, "place_北京": 0.0072553339499523495, "place_南京": 0.00017401933920426884, "place_南宁": 0.0027925836564146067, "place_厦门": 0.00031918280230402237, "place_商丘": 0.0, "place_大连": 0.00020673582277297558, "place_天津": 0.00025604091452216743, "place_威海": 0.0, "place_广州": 0.00216195412305663, "place_成都": 0.00016837359057494283, "place_无锡": 0.0013257476739828638, "place_昆明": 0.0, "place_杭州": 0.0009160575000706414, "place_深圳": 0.005275261754978397, "place_苏州": 0.00025645388692583716, "place_西安": 2.0015307038823847e-05, "place_重庆": 0.025324353845906703, "place_长沙": 2.7509986193662737e-05, "scale_中大型企业": 0.007922418384875058, "scale_中小企业": 0.011784589062808534, "scale_大型企业": 0.01607612902653479, "scale_小型企业": 0.03781965348413838, "scale_未知规模": 0.0046816140380273085, "scale_超大型企业": 0.018646875667634722, "key_word_": 0.0004403011694299308, "key_word_AI": 4.405379445647011e-05, "key_word_AI+产品": 0.0, "key_word_AI+测试": 0.0, "key_word_C++": 0.0008445971367830057, "key_word_C+++UX": 0.0, "key_word_Java": 0.006085274274710374, "key_word_Java+AI": 0.0, "key_word_Java+AI+算法": 0.0, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 0.0, "key_word_Java+Python+C++": 8.874369039008626e-05, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 3.0544536469983404e-05, "key_word_Java+Python+C+++前端+算法+测试": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 5.535773881113646e-05, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+Python+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+前端": 0.0, "key_word_Java+后端": 0.0008412200848060224, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 1.0976290213028041e-07, "key_word_PHP": 0.0, "key_word_PHP+后端": 0.00018496726599727983, "key_word_Python": 0.008847190185955376, "key_word_Python+AI": 0.0, "key_word_Python+C++": 0.0, "key_word_Python+C+++AI": 0.0, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 7.680844939455519e-06, "key_word_Python+前端+后端": 0.0, "key_word_Python+后端": 0.000762901194554491, "key_word_Python+后端+AI": 0.0, "key_word_Python+后端+大数据": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.000810925893466348, "key_word_UI": 0.0008762939360593601, "key_word_UX": 0.00032364191251072985, "key_word_android": 0.004585416203293188, "key_word_c": 0.0008908289633306407, "key_word_c语言": 0.005428590202783298, "key_word_java": 0.002578456999202053, "key_word_python": 0.002037384139733997, "key_word_web": 0.0010991277856718506, "key_word_产品": 0.0026005391441380954, "key_word_产品+运营": 1.885532806875658e-06, "key_word_人工智能+销售": 0.0, "key_word_全栈": 4.9661374302396286e-05, "key_word_前端": 0.004542568555010595, "key_word_前端+全栈": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.03281740568620434, "key_word_后端+AI": 0.0, "key_word_后端+产品": 0.0, "key_word_后端+大数据": 1.7245753916917717e-05, "key_word_后端+市场": 0.0, "key_word_后端+测试": 0.0, "key_word_大数据": 0.009480301733950423, "key_word_大数据+产品": 0.0009836039573958395, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.00014057117206579556, "key_word_大数据+运营": 0.0, "key_word_大数据+销售": 0.0, "key_word_市场": 0.0005069962063228594, "key_word_数据分析": 3.815326122417173e-05, "key_word_数据分析+大数据": 3.240052745962863e-05, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.007349261247761974, "key_word_测试+UI": 0.00017604586644509777, "key_word_测试+产品": 2.6873036433928098e-05, "key_word_爬虫": 0.0007795235295272953, "key_word_物联网": 0.004021222317876215, "key_word_算法": 0.0005378907868810785, "key_word_算法+测试": 0.0, "key_word_运营": 0.0012501359753846692, "key_word_运营+销售": 0.0, "key_word_销售": 0.0035190041656775195, "key_word_销售+市场": 0.0, "position_level_初级": 0.012289429635791913, "position_level_普通": 0.07082694420863074, "position_level_高级": 0.06655630603071346, "position_popularity_bin_中": 0.006762156928384421, "position_popularity_bin_低": 0.01698681057578379, "position_popularity_bin_极低": 0.00789328411716258, "position_popularity_bin_极高": 0.004965131157220515, "position_popularity_bin_高": 0.007141456533977179, "city_popularity_bin_中": 0.00852638131801008, "city_popularity_bin_低": 0.005117454926238577, "city_popularity_bin_极低": 0.042815297395366096, "city_popularity_bin_高": 0.0067086666635794035, "salary_ratio": 0.0, "position_popularity": 0.047541311005962525, "city_popularity": 0.059482742418555654}}}