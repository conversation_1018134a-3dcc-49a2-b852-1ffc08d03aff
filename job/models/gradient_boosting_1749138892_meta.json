{"model_type": "gradient_boosting", "model_filename": "gradient_boosting_1749138892.pkl", "created_time": "2025-06-05 23:54:52", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "gradient_boosting", "min_salary": {"mae": 0.4375249017024711, "r2": 0.0038051067563577012, "mae_orig": 8.001815339428115, "r2_orig": -0.14245001863364304}, "max_salary": {"mae": 0.28669734250355733, "r2": 0.503488066890768, "mae_orig": 6.979667119320673, "r2_orig": 0.4464234540686751}, "feature_importance": {"education_不限": 0.013802304478166686, "education_中专": 0.0024083791192499654, "education_博士": 0.004699937033192817, "education_大专": 0.055481568065104514, "education_本科": 0.011467213395171663, "education_硕士": 0.00593754909734112, "experience_1-3年": 0.10647949630818802, "experience_10年以上": 0.08453169121086307, "experience_1年以下": 0.014117348582659336, "experience_3-5年": 0.015636469597923742, "experience_5-10年": 0.04623618511065369, "experience_不限": 0.022507926373120123, "experience_应届生": 0.0025016965789021427, "place_上海": 0.007666371756481705, "place_保定": 0.0, "place_北京": 0.003335243644495109, "place_南京": 0.00039985408444289706, "place_南宁": 0.0031396395083721895, "place_厦门": 2.163604169060496e-05, "place_商丘": 0.0, "place_大连": 0.0009830280413867581, "place_天津": 0.0009262924726905459, "place_威海": 0.0, "place_广州": 0.003250601878236855, "place_成都": 0.00012654918649105388, "place_无锡": 0.000945139574470505, "place_昆明": 0.0, "place_杭州": 0.0008092114384607812, "place_深圳": 0.0040003833249675855, "place_苏州": 0.0003661465996197778, "place_西安": 0.0, "place_重庆": 0.009555954071118927, "place_长沙": 4.881749965903987e-06, "scale_中大型企业": 0.010222954615650728, "scale_中小企业": 0.011982552662572504, "scale_大型企业": 0.014941231037786488, "scale_小型企业": 0.03508234450793552, "scale_未知规模": 0.007958917990891613, "scale_超大型企业": 0.01481778687854392, "key_word_": 0.0009595836556869988, "key_word_AI": 0.000141264912192217, "key_word_AI+产品": 0.0, "key_word_AI+测试": 0.0, "key_word_C++": 0.0016226278822595627, "key_word_C+++UX": 0.0, "key_word_Java": 0.004961961762878307, "key_word_Java+AI": 0.0, "key_word_Java+AI+算法": 0.0, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 0.0, "key_word_Java+Python+C++": 0.0, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 0.0, "key_word_Java+Python+C+++前端+算法+测试": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 0.00014343162184784382, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+Python+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+前端": 0.0, "key_word_Java+后端": 0.001197137109792658, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 0.0, "key_word_PHP": 0.0005020933407180578, "key_word_PHP+后端": 0.0002754784137000555, "key_word_Python": 0.0027937666941702435, "key_word_Python+AI": 0.0, "key_word_Python+C++": 1.0411771977752884e-05, "key_word_Python+C+++AI": 0.0, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 0.00016529761827476948, "key_word_Python+前端+后端": 0.0, "key_word_Python+后端": 0.0018226897034389071, "key_word_Python+后端+AI": 0.0, "key_word_Python+后端+大数据": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+测试": 5.130271287992841e-05, "key_word_Python+算法": 0.009188253689568819, "key_word_UI": 0.00040929296271240384, "key_word_UX": 0.0008189254624896132, "key_word_android": 0.0033809268215888883, "key_word_c": 0.0003035673371755125, "key_word_c语言": 0.004081530996288494, "key_word_java": 0.0017759674666055925, "key_word_python": 0.0031463228873380004, "key_word_web": 0.0019860157598553807, "key_word_产品": 0.005064172769395167, "key_word_产品+运营": 0.0, "key_word_人工智能+销售": 0.0, "key_word_全栈": 2.0763587430539007e-05, "key_word_前端": 0.0006559613036281929, "key_word_前端+全栈": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.04218521582005888, "key_word_后端+AI": 0.0, "key_word_后端+产品": 0.0, "key_word_后端+大数据": 0.0, "key_word_后端+市场": 0.0, "key_word_后端+测试": 0.0, "key_word_大数据": 0.005088997127363457, "key_word_大数据+产品": 0.0008888848126245729, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.0001591243737347345, "key_word_大数据+运营": 0.0, "key_word_大数据+销售": 0.0, "key_word_市场": 0.0016996505153630886, "key_word_数据分析": 0.0, "key_word_数据分析+大数据": 0.0003435691163203054, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.006166927218156771, "key_word_测试+UI": 0.0011221017250736937, "key_word_测试+产品": 0.00010441600959701124, "key_word_爬虫": 0.000865191045891361, "key_word_物联网": 0.005554912316410512, "key_word_算法": 0.0014360060285029108, "key_word_算法+测试": 0.0, "key_word_运营": 0.002672832972606262, "key_word_运营+销售": 0.0, "key_word_销售": 0.004055871652172473, "key_word_销售+市场": 0.0, "position_level_初级": 0.007457821736675801, "position_level_普通": 0.01164404598882717, "position_level_高级": 0.14537908178795592, "position_popularity_bin_中": 0.006223934157444085, "position_popularity_bin_低": 0.006423195617389602, "position_popularity_bin_极低": 0.0022063583752889512, "position_popularity_bin_极高": 0.0016843193560761704, "position_popularity_bin_高": 0.0033292209018455023, "city_popularity_bin_中": 0.007942288714643394, "city_popularity_bin_低": 0.003940493683715081, "city_popularity_bin_极低": 0.048311497435659365, "city_popularity_bin_高": 0.0031137373063436485, "salary_ratio": 0.0, "position_popularity": 0.051191686169604476, "city_popularity": 0.06698538377395213}}}