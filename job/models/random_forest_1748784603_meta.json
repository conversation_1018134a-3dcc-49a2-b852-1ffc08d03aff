{"model_type": "random_forest", "model_filename": "random_forest_1748784603.pkl", "created_time": "2025-06-01 21:30:03", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.4364269446884183, "r2": 0.017989136883592494, "mae_orig": 7.265037617997218, "r2_orig": 0.18672142743965725}, "max_salary": {"mae": 0.30792856488352555, "r2": 0.4629547336291695, "mae_orig": 7.287199648163246, "r2_orig": 0.4217460612865279}, "feature_importance": {"education_不限": 0.005919731315813541, "education_博士": 0.002410241849797064, "education_大专": 0.0401965129627797, "education_本科": 0.010538288754509321, "education_硕士": 0.007728034814926461, "experience_1-3年": 0.0942710754238714, "experience_10年以上": 0.13256570906560097, "experience_1年以下": 0.011535066551668917, "experience_3-5年": 0.015085263487240012, "experience_5-10年": 0.06590054125347346, "experience_不限": 0.012222455940005462, "experience_应届生": 0.0006382926309495169, "place_上海": 0.009466350130138813, "place_北京": 0.005744892984974749, "place_南京": 5.5998652254425236e-05, "place_合肥": 0.0, "place_商丘": 0.0, "place_大连": 0.0004927859370140865, "place_天津": 8.933708524210724e-05, "place_太原": 0.0, "place_广州": 3.25566820841521e-05, "place_徐州": 0.00041014396646127675, "place_成都": 0.0001429214955264434, "place_无锡": 0.0009358493264084639, "place_昆明": 0.0, "place_杭州": 0.0002973232792606177, "place_深圳": 0.0031118153038578304, "place_温州": 0.0, "place_苏州": 0.00032675721105822944, "place_西安": 0.0001024157659121543, "place_重庆": 0.028252932212908078, "place_韶关": 0.0, "scale_中大型企业": 0.00374672032556156, "scale_中小企业": 0.006036671433697816, "scale_大型企业": 0.007616873957483241, "scale_小型企业": 0.03763549499743449, "scale_未知规模": 0.0029004605302416307, "scale_超大型企业": 0.013525009119518287, "key_word_": 0.00022012746601187926, "key_word_AI": 0.00018707983818102848, "key_word_AI+大数据": 0.0, "key_word_AI+测试": 0.0, "key_word_AI+算法": 0.0, "key_word_AI+算法+测试": 0.0, "key_word_C++": 0.0004736696784210067, "key_word_Java": 0.0032221433448853874, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 5.2991839674349346e-05, "key_word_Java+Python+C++": 0.00023217208751182615, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 5.087845636986e-06, "key_word_Java+Python+C+++前端+算法": 0.0, "key_word_Java+Python+C+++后端": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 2.0734953257857963e-06, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+全栈": 0.0, "key_word_Java+后端": 0.00017589198244622993, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 3.044625922791422e-06, "key_word_PHP": 0.0, "key_word_PHP+后端": 0.0002864859604718678, "key_word_Python": 0.006710497192092971, "key_word_Python+AI": 0.0, "key_word_Python+C++": 0.0001431183181370278, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 2.111048132039592e-05, "key_word_Python+后端": 0.001425091532744019, "key_word_Python+后端+AI": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+机器学习": 8.11798654467314e-05, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.00015530528390067958, "key_word_UI": 0.000530454334120597, "key_word_UX": 0.00029345597274686177, "key_word_android": 0.002035512161565182, "key_word_c": 0.0005804840682506278, "key_word_c语言": 0.0025769715221895823, "key_word_java": 0.000920547127780641, "key_word_python": 0.0008645405033766571, "key_word_web": 0.0011128368484820158, "key_word_产品": 0.0011575433334878383, "key_word_产品+运营": 9.30601574321743e-05, "key_word_产品+运营+市场": 0.0, "key_word_人工智能": 0.0, "key_word_全栈": 0.0, "key_word_前端": 0.0034496900211105513, "key_word_前端+产品": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.04031030979592774, "key_word_后端+产品": 0.0, "key_word_后端+全栈": 0.0, "key_word_后端+市场": 0.0, "key_word_大数据": 0.007021965988642898, "key_word_大数据+产品": 0.00038989628971328774, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.00015363743008117784, "key_word_大数据+运营": 0.0, "key_word_大数据+销售": 0.0, "key_word_市场": 0.00014522564024327855, "key_word_数据分析": 0.0, "key_word_数据分析+大数据": 0.0, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.005453068837170628, "key_word_测试+UI": 0.0015561118558876214, "key_word_测试+产品": 0.0, "key_word_爬虫": 0.0005462813223312915, "key_word_物联网": 0.0026813447319135723, "key_word_算法": 0.00046285762918213294, "key_word_算法+测试": 0.0, "key_word_运营": 0.0016182248929471812, "key_word_销售": 0.0023456913874785774, "position_level_初级": 0.011288828178618687, "position_level_普通": 0.07023260081166106, "position_level_高级": 0.10922946391209631, "position_popularity_bin_中": 0.004082886389852739, "position_popularity_bin_低": 0.01976544005551822, "position_popularity_bin_极低": 0.005708501218874537, "position_popularity_bin_极高": 0.0029309622973966946, "position_popularity_bin_高": 0.005308766147105585, "city_popularity_bin_中": 0.008506694757476209, "city_popularity_bin_低": 0.003877659182575786, "city_popularity_bin_极低": 0.05300156191216457, "city_popularity_bin_高": 0.005171330502827269, "salary_ratio": 0.0, "position_popularity": 0.03509464807725391, "city_popularity": 0.05616934941871111}}}