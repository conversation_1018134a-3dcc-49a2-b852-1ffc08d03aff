{"model_type": "random_forest", "model_filename": "random_forest_1749138977.pkl", "created_time": "2025-06-05 23:56:17", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.4476457226949853, "r2": -0.02504679407940391, "mae_orig": 7.586738080884265, "r2_orig": 0.11006555665857765}, "max_salary": {"mae": 0.30226993294136295, "r2": 0.4557292759287066, "mae_orig": 7.286031916427598, "r2_orig": 0.35967294739618494}, "feature_importance": {"education_不限": 0.007325751238434406, "education_中专": 0.0007065072234670564, "education_博士": 0.0011481377280126704, "education_大专": 0.05138701695952018, "education_本科": 0.013725884399205544, "education_硕士": 0.006666288685489841, "experience_1-3年": 0.10406708442112285, "experience_10年以上": 0.10520704802337677, "experience_1年以下": 0.011121682567082868, "experience_3-5年": 0.01338504724215151, "experience_5-10年": 0.06259799621069673, "experience_不限": 0.011139178842608686, "experience_应届生": 0.0007029763182263958, "place_上海": 0.01107162808580471, "place_保定": 0.0, "place_北京": 0.006195253694908118, "place_南京": 0.00019004732741422653, "place_南宁": 0.0026656844554239765, "place_厦门": 0.00025039915260239936, "place_商丘": 0.0, "place_大连": 0.00023494785160511805, "place_天津": 0.00016198003272795532, "place_威海": 0.0, "place_广州": 0.0019136371182290988, "place_成都": 0.00029778998845235964, "place_无锡": 0.0018160319162356588, "place_昆明": 0.0, "place_杭州": 0.0005722507982135616, "place_深圳": 0.0034861491276349872, "place_苏州": 0.0001927902270948605, "place_西安": 1.4066813085498121e-05, "place_重庆": 0.034650328746158324, "place_长沙": 6.156279664944353e-05, "scale_中大型企业": 0.003955509391009082, "scale_中小企业": 0.0060083828588821, "scale_大型企业": 0.009479353177841585, "scale_小型企业": 0.034751207327025364, "scale_未知规模": 0.0030235958457570707, "scale_超大型企业": 0.014132960428086818, "key_word_": 0.00045650515264574805, "key_word_AI": 8.509564157303085e-05, "key_word_AI+产品": 0.0, "key_word_AI+测试": 0.0, "key_word_C++": 0.0006483211105035386, "key_word_C+++UX": 0.0, "key_word_Java": 0.0032562924395159376, "key_word_Java+AI": 0.0, "key_word_Java+AI+算法": 0.0, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 0.0, "key_word_Java+Python+C++": 4.3840361678241434e-05, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 1.9517180195758332e-05, "key_word_Java+Python+C+++前端+算法+测试": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 1.0517821484983761e-05, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+Python+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+前端": 0.0, "key_word_Java+后端": 0.00048430042531849696, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 3.496513865548186e-06, "key_word_PHP": 8.419825623979728e-05, "key_word_PHP+后端": 0.000172117870377817, "key_word_Python": 0.007452660907789335, "key_word_Python+AI": 0.0, "key_word_Python+C++": 7.704261830620058e-06, "key_word_Python+C+++AI": 0.0, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 8.514332066049644e-06, "key_word_Python+前端+后端": 0.0, "key_word_Python+后端": 0.0005690029483549683, "key_word_Python+后端+AI": 0.0, "key_word_Python+后端+大数据": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.0005836456876072765, "key_word_UI": 0.0008589013237377066, "key_word_UX": 0.00021208330488973765, "key_word_android": 0.0025816809558383553, "key_word_c": 0.0006180262788838981, "key_word_c语言": 0.003245907972993277, "key_word_java": 0.0025369180453120297, "key_word_python": 0.0011347713095441064, "key_word_web": 0.0011778946196408942, "key_word_产品": 0.001653410341717078, "key_word_产品+运营": 1.1376034068118909e-05, "key_word_人工智能+销售": 0.0, "key_word_全栈": 6.887266042246621e-05, "key_word_前端": 0.002354296749495585, "key_word_前端+全栈": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.034333988572710326, "key_word_后端+AI": 0.0, "key_word_后端+产品": 5.498165315269212e-05, "key_word_后端+大数据": 6.813605501618874e-06, "key_word_后端+市场": 0.0, "key_word_后端+测试": 0.0, "key_word_大数据": 0.010691962582722956, "key_word_大数据+产品": 0.0012295145612040523, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 8.155599801421935e-05, "key_word_大数据+运营": 8.72008529142225e-06, "key_word_大数据+销售": 0.0, "key_word_市场": 0.0004523016404687356, "key_word_数据分析": 1.261007594204742e-05, "key_word_数据分析+大数据": 8.376834307681446e-06, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.0047269274756106434, "key_word_测试+UI": 5.187781350345649e-05, "key_word_测试+产品": 1.304957522880441e-05, "key_word_爬虫": 0.0005041253782703584, "key_word_物联网": 0.0022949609783755303, "key_word_算法": 0.000617508618759774, "key_word_算法+测试": 7.749722546483518e-07, "key_word_运营": 0.001753345364618877, "key_word_运营+销售": 0.0, "key_word_销售": 0.004038991886163268, "key_word_销售+市场": 0.0, "position_level_初级": 0.011968598177744514, "position_level_普通": 0.07063544717116665, "position_level_高级": 0.1001640602644198, "position_popularity_bin_中": 0.00428133829142875, "position_popularity_bin_低": 0.019782386064660425, "position_popularity_bin_极低": 0.00494592454796693, "position_popularity_bin_极高": 0.0029405757748116857, "position_popularity_bin_高": 0.004665623806883063, "city_popularity_bin_中": 0.00877649369250735, "city_popularity_bin_低": 0.003924892198758304, "city_popularity_bin_极低": 0.04777148675466328, "city_popularity_bin_高": 0.006120908176357106, "salary_ratio": 0.0, "position_popularity": 0.036678647885031654, "city_popularity": 0.06181520199367108}}}