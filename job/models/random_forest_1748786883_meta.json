{"model_type": "random_forest", "model_filename": "random_forest_1748786883.pkl", "created_time": "2025-06-01 22:08:03", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.4326919280167, "r2": 0.039841169506581675, "mae_orig": 7.304686527512525, "r2_orig": 0.16293242754169768}, "max_salary": {"mae": 0.299339053622369, "r2": 0.48090762441575563, "mae_orig": 7.067218923463152, "r2_orig": 0.4475721876171992}, "feature_importance": {"education_不限": 0.009234310018583007, "education_博士": 0.0020943442949386973, "education_大专": 0.04268093639434001, "education_本科": 0.01609262671094234, "education_硕士": 0.008426605362729166, "experience_1-3年": 0.08796482546524097, "experience_10年以上": 0.10411730822943227, "experience_1年以下": 0.010834637634999776, "experience_3-5年": 0.02033181475130674, "experience_5-10年": 0.059972507587387855, "experience_不限": 0.018364713474642594, "experience_应届生": 0.0009389066907424974, "place_上海": 0.00877470751958081, "place_北京": 0.006379835040874718, "place_南京": 8.28411568662993e-05, "place_合肥": 0.0, "place_商丘": 0.0, "place_大连": 0.00038985093433740645, "place_天津": 0.00023864413382060273, "place_太原": 0.0, "place_广州": 0.00017687065786174975, "place_徐州": 0.00021629942865107268, "place_成都": 0.0001772445596457057, "place_无锡": 0.0008970143220677765, "place_昆明": 0.0, "place_杭州": 0.0005054572200422205, "place_深圳": 0.005702344555327872, "place_温州": 0.0, "place_苏州": 0.00040317177725343893, "place_西安": 0.0002704680092318156, "place_重庆": 0.03171768478983897, "place_韶关": 0.0, "scale_中大型企业": 0.008236697648788456, "scale_中小企业": 0.013049804626012995, "scale_大型企业": 0.014934084087581307, "scale_小型企业": 0.03482358854661905, "scale_未知规模": 0.003709257724865714, "scale_超大型企业": 0.020436209866146197, "key_word_": 0.0002427557752446805, "key_word_AI": 0.00011046355783303145, "key_word_AI+大数据": 0.0, "key_word_AI+测试": 0.0, "key_word_AI+算法": 0.0, "key_word_AI+算法+测试": 0.0, "key_word_C++": 0.0005626785788020899, "key_word_Java": 0.005342184701752713, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 9.403566419200968e-05, "key_word_Java+Python+C++": 0.0002738739152635213, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 9.656308494146663e-06, "key_word_Java+Python+C+++前端+算法": 0.0, "key_word_Java+Python+C+++后端": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 1.296642755973184e-05, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+全栈": 0.0, "key_word_Java+后端": 0.0007614142514241925, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 8.624599839316986e-06, "key_word_PHP": 0.0, "key_word_PHP+后端": 0.00028619261540123, "key_word_Python": 0.007817066000269147, "key_word_Python+AI": 0.0, "key_word_Python+C++": 3.872438128162566e-05, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 1.1387852803574892e-05, "key_word_Python+后端": 0.0012600697636502424, "key_word_Python+后端+AI": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+机器学习": 9.039798698551562e-05, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.00015167547856036588, "key_word_UI": 0.0011236402244568329, "key_word_UX": 0.0004206917125981264, "key_word_android": 0.004685689067957801, "key_word_c": 0.00080402509063659, "key_word_c语言": 0.004659717904257025, "key_word_java": 0.0011016248506894085, "key_word_python": 0.0013850795017208183, "key_word_web": 0.0013505905982515993, "key_word_产品": 0.0019397840767994912, "key_word_产品+运营": 0.00010034307559083428, "key_word_产品+运营+市场": 0.0, "key_word_人工智能": 0.0, "key_word_全栈": 4.3157299388705855e-06, "key_word_前端": 0.004528259691609578, "key_word_前端+产品": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.03674356669119912, "key_word_后端+产品": 0.0, "key_word_后端+全栈": 0.0, "key_word_后端+市场": 0.0, "key_word_大数据": 0.008731203887014212, "key_word_大数据+产品": 0.0005440906135480845, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.00024315509261447512, "key_word_大数据+运营": 7.897788252685849e-06, "key_word_大数据+销售": 0.0, "key_word_市场": 0.0002366761400814056, "key_word_数据分析": 1.7571952867447345e-05, "key_word_数据分析+大数据": 1.850596807379693e-05, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.007612180811562825, "key_word_测试+UI": 0.0014723969759669652, "key_word_测试+产品": 3.223265551648886e-05, "key_word_爬虫": 0.0009861229676565377, "key_word_物联网": 0.004260900686216715, "key_word_算法": 0.0005504510314925763, "key_word_算法+测试": 0.0, "key_word_运营": 0.0020070080068300463, "key_word_销售": 0.0027442067058778665, "position_level_初级": 0.011121555250041552, "position_level_普通": 0.057929838775629135, "position_level_高级": 0.09180357943139317, "position_popularity_bin_中": 0.006236414836137105, "position_popularity_bin_低": 0.02024541172961434, "position_popularity_bin_极低": 0.007237361114100199, "position_popularity_bin_极高": 0.0047081947618899025, "position_popularity_bin_高": 0.007024900801680462, "city_popularity_bin_中": 0.00955835966968454, "city_popularity_bin_低": 0.005470146707844856, "city_popularity_bin_极低": 0.04168098227545001, "city_popularity_bin_高": 0.006554113995772316, "salary_ratio": 0.0, "position_popularity": 0.04528248738653009, "city_popularity": 0.04758494268489688}}}