{"model_type": "random_forest", "model_filename": "random_forest_1749138875.pkl", "created_time": "2025-06-05 23:54:35", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.4460160021959057, "r2": -0.01802578418175549, "mae_orig": 7.540192393152429, "r2_orig": 0.11849011325529313}, "max_salary": {"mae": 0.30161992602822546, "r2": 0.45946699793108614, "mae_orig": 7.273586924578796, "r2_orig": 0.36186973981018167}, "feature_importance": {"education_不限": 0.00788673072701967, "education_中专": 0.000642818872533442, "education_博士": 0.0015820355980035196, "education_大专": 0.045812534259014104, "education_本科": 0.01547914816336041, "education_硕士": 0.006532381161038569, "experience_1-3年": 0.10203770607297066, "experience_10年以上": 0.10690217684153731, "experience_1年以下": 0.014112016395691169, "experience_3-5年": 0.014781809317301957, "experience_5-10年": 0.06716977242918525, "experience_不限": 0.011144611618303966, "experience_应届生": 0.0006928135661288694, "place_上海": 0.008535069975140674, "place_保定": 0.0, "place_北京": 0.005187983252076442, "place_南京": 6.220084522868906e-05, "place_南宁": 0.0029851301622137527, "place_厦门": 0.00023297961931237156, "place_商丘": 0.0, "place_大连": 0.00012734074464123519, "place_天津": 5.804826206815745e-05, "place_威海": 0.0, "place_广州": 0.0022190183681839995, "place_成都": 0.00019752821228527623, "place_无锡": 0.0014056021074531592, "place_昆明": 0.0, "place_杭州": 0.0006823502331610574, "place_深圳": 0.003123540916414608, "place_苏州": 0.00018532766525226167, "place_西安": 3.0328213647055713e-05, "place_重庆": 0.032752624855439456, "place_长沙": 9.422540828514022e-05, "scale_中大型企业": 0.0030865536408811486, "scale_中小企业": 0.005775869682631388, "scale_大型企业": 0.008820568660571699, "scale_小型企业": 0.038420101597576625, "scale_未知规模": 0.003493935717980455, "scale_超大型企业": 0.01328276467244396, "key_word_": 0.000682143861817295, "key_word_AI": 0.00011246697174042752, "key_word_AI+产品": 0.0, "key_word_AI+测试": 0.0, "key_word_C++": 0.0007270060632589218, "key_word_C+++UX": 0.0, "key_word_Java": 0.0032307640982658886, "key_word_Java+AI": 0.0, "key_word_Java+AI+算法": 0.0, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 0.0, "key_word_Java+Python+C++": 6.908102807960393e-05, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 2.3181343301548824e-05, "key_word_Java+Python+C+++前端+算法+测试": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 2.0261396359422014e-05, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+Python+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+前端": 0.0, "key_word_Java+后端": 0.0005873113376075299, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 0.0, "key_word_PHP": 0.0, "key_word_PHP+后端": 0.00017520034315736884, "key_word_Python": 0.00659949120724552, "key_word_Python+AI": 0.0, "key_word_Python+C++": 0.0, "key_word_Python+C+++AI": 0.0, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 1.2637762867040078e-05, "key_word_Python+前端+后端": 0.0, "key_word_Python+后端": 0.00046782733466109694, "key_word_Python+后端+AI": 0.0, "key_word_Python+后端+大数据": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.0008373370864968257, "key_word_UI": 0.0009745369391225235, "key_word_UX": 0.0001330079907116525, "key_word_android": 0.0026419024706735273, "key_word_c": 0.000737600882464976, "key_word_c语言": 0.0038919248355839034, "key_word_java": 0.0027513466034723, "key_word_python": 0.0015828606054141454, "key_word_web": 0.0007857562387682923, "key_word_产品": 0.0017564649823281347, "key_word_产品+运营": 0.0, "key_word_人工智能+销售": 0.0, "key_word_全栈": 4.800479565046527e-05, "key_word_前端": 0.0034955466405803257, "key_word_前端+全栈": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.03440195929706678, "key_word_后端+AI": 0.0, "key_word_后端+产品": 5.892680148093662e-05, "key_word_后端+大数据": 8.8883351748371e-06, "key_word_后端+市场": 0.0, "key_word_后端+测试": 0.0, "key_word_大数据": 0.00970029784615107, "key_word_大数据+产品": 0.0012549588479490532, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 5.458929271974947e-05, "key_word_大数据+运营": 0.0, "key_word_大数据+销售": 0.0, "key_word_市场": 0.00023314609931380034, "key_word_数据分析": 4.782645390496905e-05, "key_word_数据分析+大数据": 2.3566775681320615e-05, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.004853859202933477, "key_word_测试+UI": 2.8556760711995805e-05, "key_word_测试+产品": 5.1385636938353334e-05, "key_word_爬虫": 0.000510857210650651, "key_word_物联网": 0.00293607914949122, "key_word_算法": 0.0006194495216691157, "key_word_算法+测试": 0.0, "key_word_运营": 0.0011751308452357112, "key_word_运营+销售": 0.0, "key_word_销售": 0.003185102949690973, "key_word_销售+市场": 0.0, "position_level_初级": 0.013203421246299309, "position_level_普通": 0.07412444857137632, "position_level_高级": 0.08779335687022666, "position_popularity_bin_中": 0.003367064855884705, "position_popularity_bin_低": 0.01919944884434127, "position_popularity_bin_极低": 0.005507844096559936, "position_popularity_bin_极高": 0.002184925856680299, "position_popularity_bin_高": 0.004618176337733021, "city_popularity_bin_中": 0.007627019803981712, "city_popularity_bin_低": 0.0036087277875517625, "city_popularity_bin_极低": 0.05408203626589693, "city_popularity_bin_高": 0.004435738598669184, "salary_ratio": 0.0, "position_popularity": 0.03532013889551331, "city_popularity": 0.06790376028791548}}}