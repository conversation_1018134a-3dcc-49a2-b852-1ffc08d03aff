{"model_type": "random_forest", "model_filename": "random_forest_1748783302.pkl", "created_time": "2025-06-01 21:08:22", "categorical_features": ["education", "experience", "place", "scale", "key_word", "position_level", "position_popularity_bin", "city_popularity_bin"], "numerical_features": ["salary_ratio", "position_popularity", "city_popularity"], "results": {"model_type": "random_forest", "min_salary": {"mae": 0.43314623371505134, "r2": 0.03700168765738254, "mae_orig": 7.287212583334496, "r2_orig": 0.16980604634461527}, "max_salary": {"mae": 0.3017763022065132, "r2": 0.47772112539046685, "mae_orig": 7.129485506306298, "r2_orig": 0.4407467844100589}, "feature_importance": {"education_不限": 0.008780596403253934, "education_博士": 0.002129968786630127, "education_大专": 0.041328256643752496, "education_本科": 0.015759573133151766, "education_硕士": 0.007813336591712851, "experience_1-3年": 0.08850263151593264, "experience_10年以上": 0.10806540225205417, "experience_1年以下": 0.011254967593419, "experience_3-5年": 0.019251551831453047, "experience_5-10年": 0.06229260786272599, "experience_不限": 0.016133950159862586, "experience_应届生": 0.0007523066279929127, "place_上海": 0.008478751747144377, "place_北京": 0.006514618379411069, "place_南京": 0.00014458187354234522, "place_合肥": 0.0, "place_商丘": 0.0, "place_大连": 0.00041294554323535135, "place_天津": 0.00018797842167494943, "place_太原": 0.0, "place_广州": 0.00011991753200211513, "place_徐州": 0.0003637900956837617, "place_成都": 0.00019772252844253783, "place_无锡": 0.0009876373041525692, "place_昆明": 0.0, "place_杭州": 0.00043750531067994746, "place_深圳": 0.005395482449378009, "place_温州": 0.0, "place_苏州": 0.0004117166661012895, "place_西安": 0.00032022082185642815, "place_重庆": 0.03488968104424822, "place_韶关": 0.0, "scale_中大型企业": 0.00645877557244088, "scale_中小企业": 0.010875891350241432, "scale_大型企业": 0.012736827716145389, "scale_小型企业": 0.03559676772273595, "scale_未知规模": 0.003375785261889255, "scale_超大型企业": 0.01869078591102699, "key_word_": 0.000268225509459083, "key_word_AI": 6.948267457437922e-05, "key_word_AI+大数据": 0.0, "key_word_AI+测试": 0.0, "key_word_AI+算法": 0.0, "key_word_AI+算法+测试": 0.0, "key_word_C++": 0.0005984270290961528, "key_word_Java": 0.004742289499722519, "key_word_Java+C++": 0.0, "key_word_Java+C+++前端+测试": 0.0, "key_word_Java+C+++测试": 0.0, "key_word_Java+Python": 5.207540183420018e-05, "key_word_Java+Python+C++": 0.00024238160532645225, "key_word_Java+Python+C+++AI+算法+测试": 0.0, "key_word_Java+Python+C+++前端+后端+测试": 0.0, "key_word_Java+Python+C+++前端+测试": 1.0649685864305551e-05, "key_word_Java+Python+C+++前端+算法": 0.0, "key_word_Java+Python+C+++后端": 0.0, "key_word_Java+Python+C+++大数据+测试": 0.0, "key_word_Java+Python+C+++测试": 1.8490742270140305e-05, "key_word_Java+Python+前端+测试": 0.0, "key_word_Java+产品": 0.0, "key_word_Java+全栈": 0.0, "key_word_Java+后端": 0.0007042515224870026, "key_word_Java+后端+AI": 0.0, "key_word_Java+大数据": 6.517160783201223e-06, "key_word_PHP": 0.0, "key_word_PHP+后端": 0.0004641724266637725, "key_word_Python": 0.007667880129913363, "key_word_Python+AI": 0.0, "key_word_Python+C++": 3.49649631344207e-05, "key_word_Python+人工智能": 0.0, "key_word_Python+全栈": 2.347998778711435e-05, "key_word_Python+后端": 0.001288281248304429, "key_word_Python+后端+AI": 0.0, "key_word_Python+大数据": 0.0, "key_word_Python+数据分析": 0.0, "key_word_Python+机器学习": 0.00010186683882496899, "key_word_Python+测试": 0.0, "key_word_Python+算法": 0.00014731066575709675, "key_word_UI": 0.0010035845947122175, "key_word_UX": 0.0003456188835638383, "key_word_android": 0.004052552091821313, "key_word_c": 0.0006584478610597358, "key_word_c语言": 0.004165620271958865, "key_word_java": 0.001088803277767023, "key_word_python": 0.0010608166114718016, "key_word_web": 0.0009835653538056307, "key_word_产品": 0.001895051683236619, "key_word_产品+运营": 0.00010889525310697594, "key_word_产品+运营+市场": 0.0, "key_word_人工智能": 0.0, "key_word_全栈": 1.2688280883483292e-06, "key_word_前端": 0.004494228647653766, "key_word_前端+产品": 0.0, "key_word_前端+后端": 0.0, "key_word_前端+运营": 0.0, "key_word_后端": 0.036791017402985945, "key_word_后端+产品": 0.0, "key_word_后端+全栈": 0.0, "key_word_后端+市场": 0.0, "key_word_大数据": 0.008317022703355005, "key_word_大数据+产品": 0.0005120680125629763, "key_word_大数据+测试": 0.0, "key_word_大数据+算法": 0.0002421121943846468, "key_word_大数据+运营": 4.253100389296043e-06, "key_word_大数据+销售": 0.0, "key_word_市场": 0.000227510935253199, "key_word_数据分析": 4.37289771089489e-06, "key_word_数据分析+大数据": 4.715356884362818e-05, "key_word_机器学习+大数据": 0.0, "key_word_机器学习+大数据+算法": 0.0, "key_word_测试": 0.006653712242570453, "key_word_测试+UI": 0.001313108952310951, "key_word_测试+产品": 2.3921447136085404e-05, "key_word_爬虫": 0.0008608554112285957, "key_word_物联网": 0.0038504896049038314, "key_word_算法": 0.0005469233647123038, "key_word_算法+测试": 0.0, "key_word_运营": 0.0022175885234976675, "key_word_销售": 0.0027034364304777347, "position_level_初级": 0.01195790956798693, "position_level_普通": 0.06345702997290079, "position_level_高级": 0.09747852389944456, "position_popularity_bin_中": 0.006390036565866006, "position_popularity_bin_低": 0.019016248825275132, "position_popularity_bin_极低": 0.0077390320292788015, "position_popularity_bin_极高": 0.0042436656026225575, "position_popularity_bin_高": 0.006454000534461283, "city_popularity_bin_中": 0.008536418437954572, "city_popularity_bin_低": 0.005168551638206815, "city_popularity_bin_极低": 0.04263358932006307, "city_popularity_bin_高": 0.006068794705786944, "salary_ratio": 0.0, "position_popularity": 0.04253876206234847, "city_popularity": 0.0490401569414578}}}