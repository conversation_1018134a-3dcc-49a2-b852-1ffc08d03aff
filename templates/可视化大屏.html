<html>
<head>
    <meta charset="utf-8">
    <title>计算机行业就业情况可视化大屏</title>
    <script type="text/javascript" src="/static/js/jquery.js"></script>
    <script type="text/javascript" src="/static/js/echarts.min.js"></script>
    <link rel="stylesheet" href="/static/css/comon0.css">
    <style>
        /* 添加自适应背景样式 */
        .canvas {
            position: fixed;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            z-index: 1;
            overflow: hidden;
        }
        
        .canvas iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        /* 确保内容在背景上方 */
        .head, .mainbox, .back {
            position: relative;
            z-index: 2;
        }
    </style>
</head>
<script>
    $(window).load(function () {
        $(".loading").fadeOut()
    })


    $(document).ready(function () {
        var whei = $(window).width()
        $("html").css({fontSize: whei / 20})
        $(window).resize(function () {
            var whei = $(window).width()
            $("html").css({fontSize: whei / 20})
        });
    });
</script>
<!--右下角文字滚动效果-->
<script type="text/javascript">
    $(document).ready(function () {
        var html = $(".wrap ul").html()
        $(".wrap ul").append(html)
        var ls = $(".wrap li").length / 2 + 1
        i = 0
        ref = setInterval(function () {
            i++
            if (i == ls) {
                i = 1
                $(".wrap ul").css({marginTop: 0})
                $(".wrap ul").animate({marginTop: -'.52' * i + 'rem'}, 1000)
            }
            $(".wrap ul").animate({marginTop: -'.52' * i + 'rem'}, 1000)


        }, 2400);
    })
</script>

<script>
    // 定义变量接收后端传递的数据
    var my = {
        var_1: {{ job|safe }}
    };
    var my1 = {
        var_2: {{ job1|safe }}
    };
    var geo = {
        geo_1: {{ data2|safe }}
    };
    var job_prices = {
        job_price_index: {{ job_price_index|safe }},
        job_price: {{ job_price|safe }},
        java_cities_price: {{ java_cities_price|safe }},
        python_cities_price: {{ python_cities_price|safe }},
        web_cities_price: {{ web_cities_price|safe }},
        hadoop_cities_price: {{ hadoop_cities_price|safe }}
    };
    var job_ability = {
        abi_name: {{ abi_name|safe }},
        abi_snum: {{ abi_snum|safe }}
    };
    // 技能薪资数据
    var skill_salary = {
        names: {{ skill_names|safe }},
        salaries: {{ skill_salaries|safe }}
    };
    var job_titles = {{ job_titles | safe }};
    var filtered_data = {{ filtered_data | safe }};
    
    // 打印城市数据，用于验证
    console.log("城市名称:", my.var_1);
    console.log("城市职位数量:", my1.var_2);
    console.log("技能薪资数据:", skill_salary);
</script>
<script language="JavaScript" src="/static/js/js.js"></script>
<body>
<div class="canvas" style="opacity: .2">
    <iframe frameborder="0" src="/static/js/index.html"></iframe>
</div>
<div class="loading">
    <div class="loadbox"><img src="/static/images/loading.gif"> 页面加载中...</div>
</div>
<div class="head">
    <h1>计算机行业就业情况可视化大屏</h1>
    <div class="weather"><!--<img src="images/weather.png"><span>多云转小雨</span>--><span id="showTime"></span></div>
    <script>
        var t = null;
        t = setTimeout(time, 1000);

        function time() {
            clearTimeout(t);
            dt = new Date();
            var y = dt.getFullYear();
            var mt = dt.getMonth() + 1;
            var day = dt.getDate();
            var h = dt.getHours();
            var m = dt.getMinutes();
            var s = dt.getSeconds();
            document.getElementById("showTime").innerHTML = y + "年" + mt + "月" + day + "-" + h + "时" + m + "分" + s + "秒";
            t = setTimeout(time, 1000);
        }
    </script>
</div>


<div class="mainbox">
    <ul class="clearfix">
        <li>
            <div class="boxall" style="height: 3.2rem">
                <div class="alltitle">部分各城市招聘岗位数量图</div>
                <div class="allnav" id="echart1"></div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height: 3.2rem">
                <div class="alltitle">不同学历下的平均薪资图(单位:K)</div>
                <div class="allnav" id="echart2"></div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height: 3.2rem">
                <div style="height:100%; width: 100%;">
                    <div class="sy" id="fb1"></div>
                </div>
                <div class="boxfoot">
                </div>
            </div>
        </li>


        <li>
            <div class="bar">
                <div class="barbox">
                    <ul class="clearfix">
                        <li class="pulll_left counter">{{ lendata }}</li>
                        <li class="pulll_left counter">{{ unique_job_count }}</li>
                    </ul>
                </div>
                <div class="barbox2">
                    <ul class="clearfix">
                        <li class="pulll_left">岗位数量总数</li>
                        <li class="pulll_left">岗位类型总数</li>
                    </ul>
                </div>
            </div>
            <div class="map">
                <div class="map1"><img src="/static/images/lbx.png"></div>
                <div class="map2"><img src="/static/images/jt.png"></div>
                <div class="map3"><img src="/static/images/map.png"></div>    <!-- 这里是三个图片压在一块 -->
                <div class="map4" id="map_1"></div>
            </div>
        </li>


        <li>
            <div class="boxall" style="height:3.4rem">
                <div class="alltitle">热门城市Java/Python/Web/算法岗位平均薪资对比(单位:K)</div>
                <div class="allnav" id="echart4"></div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height: 3.2rem">
                <div class="alltitle">北京地区各岗位数量图</div>
                <div class="allnav" id="echart6"></div>
                <div class="boxfoot"></div>
            </div>
            <div class="boxall" style="height: 3rem">
                <div class="alltitle">各大公司招聘岗位数top10</div>
                <div class="wrap">
                    <ul>
                        {% for item in ll %}
                            <li><p>{{ item }} </p></li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="boxfoot"></div>
            </div>
        </li>
    </ul>
</div>
<div class="back"></div>


<script type="text/javascript" src="/static/js/china.js"></script>
<script type="text/javascript" src="/static/js/area_echarts.js"></script>


</body>
</html>
