{% extends "base.html" %}
{% load static %}
{% block content %}
<body>
<div class="layui-fluid layadmin-maillist-fluid">
  <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
            <blockquote class="layui-elem-quote" style="background-color: white">用户越多，投递的简历越多,推荐的越准确</blockquote>
      </div>
      
      {% if error %}
      <div class="layui-col-md12">
          <div class="layui-card">
              <div class="layui-card-header">提示</div>
              <div class="layui-card-body">
                  <div class="layui-text" style="text-align: center; padding: 30px 0;">
                      <i class="layui-icon layui-icon-face-cry" style="font-size: 50px; color: #FF5722;"></i>
                      <p style="margin-top: 10px; font-size: 16px;">{{ error }}</p>
                  </div>
              </div>
          </div>
      </div>
      {% endif %}
      
      {% if recommend_list %}
          {% for job in recommend_list %}
            <div class="layui-col-md4 layui-col-sm6">
          <div class="layadmin-contact-box" >
              <div class="layui-col-md12 layui-col-sm6">
                <a href="javascript:;">
                  <div class="layadmin-text">
                    <h3 class="layadmin-title"><strong>职位名称：{{ job.name | truncatechars:16 }}</strong></h3>
                    <h3 class="layadmin-title">薪资待遇：{{ job.salary }}</h3>
                    <h3 class="layadmin-title">学历要求：{{ job.education }}</h3>
                    <h3 class="layadmin-title">经验要求：{{ job.experience }}</h3>
                    <h3 class="layadmin-title">工作地点：<i class="layui-icon layui-icon-location"></i>{{ job.place }}</h3>
                    <h3 class="layadmin-title">公司名称：{{ job.company | truncatechars:16 }}</h3>
                    <h3 class="layadmin-title">
                      评分：<div class="layui-inline layui-rate" id="rating-{{ job.job_id }}" data-value="{{ job.rating|default:0 }}"></div>
                    </h3>
                  </div>
                </a>
              </div>
              <div class="layui-col-md12 layui-col-sm6 layui-mt10">
                <div class="layui-btn-group">
                  {% if job.is_favorite %}
                    <button class="layui-btn layui-btn-warm toggle-favorite" data-jobid="{{ job.job_id }}" data-favorite="true">
                      <i class="layui-icon layui-icon-star-fill"></i> 已收藏
                    </button>
                  {% else %}
                    <button class="layui-btn layui-btn-primary toggle-favorite" data-jobid="{{ job.job_id }}" data-favorite="false">
                      <i class="layui-icon layui-icon-star"></i> 收藏
                    </button>
                  {% endif %}
                  
                  {% if job.is_sent %}
                    <button class="layui-btn layui-btn-normal" disabled>已投递</button>
                  {% else %}
                    <button class="layui-btn" onclick="send({{ job.job_id }},'{{ job.name }}')">投递职位</button>
                  {% endif %}
                </div>
              </div>
          </div>
        </div>
          {% endfor %}
      {% else %}
          {% if not error %}
          <div class="layui-col-md12">
              <div class="layui-card">
                  <div class="layui-card-header">提示</div>
                  <div class="layui-card-body">
                      <div class="layui-text" style="text-align: center; padding: 30px 0;">
                          <i class="layui-icon layui-icon-face-smile" style="font-size: 50px; color: #1E9FFF;"></i>
                          <p style="margin-top: 10px; font-size: 16px;">暂无推荐职位，请尝试投递更多简历来获取更准确的推荐</p>
                      </div>
                  </div>
              </div>
          </div>
          {% endif %}
      {% endif %}
  </div>
</div>

<!-- 添加全局样式，确保评分组件显示更美观 -->
<style>
  .layui-rate {
    display: inline-block;
    vertical-align: middle;
    min-width: 180px !important;
    padding-right: 15px;
    overflow: visible !important;
  }
  .layui-rate span i {
    margin-right: 3px;
  }
  .layui-rate-text {
    white-space: nowrap;
  }
  .layadmin-title {
    margin-bottom: 10px;
    line-height: 24px;
  }
  /* 确保评分卡片更大 */
  .layadmin-contact-box {
    min-height: 280px;
    padding: 15px;
  }
</style>

</body>
{% endblock %}
{% block js %}
  <script>
  layui.config({
    base: '{% static "layuiadmin/" %}' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'jquery', 'layer', 'element', 'rate'], function(){
      var $ = layui.$;
      var layer = layui.layer;
      var element = layui.element;
      var rate = layui.rate;
      
      // 渲染评分组件
      {% for job in recommend_list %}
      rate.render({
        elem: '#rating-{{ job.job_id }}',
        value: {{ job.rating|default:0 }},
        text: true,
        half: false,
        readonly: false,
        theme: '#FFB800',  // 主题色
        length: 5,         // 星星个数
        choose: function(value){
          // 提交评分
          $.ajax({
            type: 'POST',
            url: '/job_rate/',
            data: {
              job_id: '{{ job.job_id }}',
              rating: value
            },
            success: function(res){
              if(res.code === 0){
                layer.msg(res.msg);
              } else {
                layer.msg(res.msg);
              }
            }
          });
        }
      });
      {% endfor %}
      
      // 收藏/取消收藏
      $('.toggle-favorite').on('click', function(){
          var btn = $(this);
          var jobId = btn.data('jobid');
          
          $.ajax({
              type: 'POST',
              url: '/toggle_favorite/',
              data: {
                  job_id: jobId
              },
              success: function(res){
                  if(res.code === 0){
                      layer.msg(res.msg);
                      // 更新按钮状态
                      if(res.is_favorite){
                          btn.data('favorite', true);
                          btn.removeClass('layui-btn-primary').addClass('layui-btn-warm');
                          btn.html('<i class="layui-icon layui-icon-star-fill"></i> 已收藏');
                      } else {
                          btn.data('favorite', false);
                          btn.removeClass('layui-btn-warm').addClass('layui-btn-primary');
                          btn.html('<i class="layui-icon layui-icon-star"></i> 收藏');
                      }
                  } else {
                      layer.msg(res.msg);
                  }
              }
          });
      });
  });

  function send(job_id, job_name) {
      var $ = layui.$;
      var layer = layui.layer;
      var element = layui.element;

      console.log(job_id);
      layer.confirm("确定投递职位"+job_name +"吗？", function(t) {
        $.ajax({
            type: 'POST',
            data:{"job_id":job_id, "send_key":0},
            url: '/send_job/',
            success: function (res) {
                layer.msg(res.msg);
                location.reload();
            },
            error:function(response){
                layer.msg(response.msg);
            }
        });
        layer.close(t);
      });
  }
  </script>
{% endblock js %}