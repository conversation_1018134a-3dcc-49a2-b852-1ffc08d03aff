{% extends "base.html" %}
{% load static %}
{% block content %}
    <body class="layui-layout-body">
    <div id="LAY_app">
        <div class="layui-layout layui-layout-admin">
            <div class="layui-header">
                <!-- 头部区域 -->
                <ul class="layui-nav layui-layout-left">
                    <li class="layui-nav-item layadmin-flexible" lay-unselect>
                        <a href="javascript:;" layadmin-event="flexible" title="侧边伸缩">
                            <i class="layui-icon layui-icon-shrink-right" id="LAY_app_flexible"></i>
                        </a>
                    </li>
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;" layadmin-event="refresh" title="刷新">
                            <i class="layui-icon layui-icon-refresh-3"></i>
                        </a>
                    </li>
                </ul>
                <ul class="layui-nav layui-layout-right" lay-filter="layadmin-layout-right">
                    <li class="layui-nav-item" lay-unselect>
                        <a href="javascript:;">
                            <cite>{{ request.session.user_name }}</cite>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a lay-href="{% url "pass_page" %}">修改密码</a></dd>
                            <hr>
                            <dd style="text-align: center;"><a href="{% url "logout" %}">退出系统</a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item layui-hide-xs" lay-unselect>
                        <a href="javascript:;" layadmin-event="fullscreen">
                            <i class="layui-icon layui-icon-screen-full"></i>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 侧边菜单 -->
            <div class="layui-side layui-side-menu">
                <div class="layui-side-scroll">
                    <div class="layui-logo">
                        <span>职位信息可视化推荐系统</span>
                    </div>
                    <ul class="layui-nav layui-nav-tree" lay-shrink="all" id="LAY-system-side-menu"
                        lay-filter="layadmin-system-side-menu">
                        <li data-name="home" class="layui-nav-item layui-nav-itemed">
                            <a href="javascript:;" lay-tips="主页">
                                <i class="layui-icon layui-icon-home"></i>
                                <cite>主页</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd data-name="console" class="layui-this">
                                    <a lay-href="{% url "welcome" %}">控制台</a>
                                </dd>
                            </dl>
                        </li>
                        <li data-name="template" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="数据管理">
                                <i class="layui-icon layui-icon-release"></i>
                                <cite>爬虫调度</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a lay-href="{% url "spiders" %}">数据爬取</a></dd>
                            </dl>
                        </li>
                        <li data-name="template" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="数据管理">
                                <i class="layui-icon layui-icon-search"></i>
                                <cite>数据管理</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd><a lay-href="{% url "job_list" %}">数据检索</a></dd>
                                <dd>
                                    <a href="javascript:;">数据可视化</a>
                                    <dl class="layui-nav-child">
                                        <dd data-name="list"><a lay-href="{% url "data_visualization" %}">数据可视化分析</a></dd>
                                        <dd data-name="list"><a lay-href="{% url 'salary' %}">薪资待遇分布</a></dd>
                                        <dd data-name="tags"><a lay-href="{% url "edu" %}">学历要求分布</a></dd>
                                        <dd data-name="tags"><a lay-href="{% url "bar_page" %}">职位关键字</a></dd>
                                        <dd data-name="tags"><a lay-href="{% url "skill_heatmap" %}">技能热度地图</a></dd>
                                        <dd data-name="dashboard"><a href="{% url "visualization_dashboard" %}" target="_blank">可视化大屏</a></dd>
                                    </dl>
                                </dd>
                            </dl>
                        </li>
                        <li data-name="senior" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="职位推荐">
                                <i class="layui-icon layui-icon-senior"></i>
                                <cite>职位推荐</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd>
                                    <a lay-href="{% url "job_expect" %}">求职意向</a>
                                </dd>
                                <dd>
                                    <a lay-href="{% url "get_recommend" %}">职位推荐</a>
                                </dd>
                                <dd>
                                    <a lay-href="{% url "favorite_jobs" %}">收藏职位</a>
                                </dd>
                                <dd>
                                    <a lay-href="{% url "send_page" %}">求职列表</a>
                                </dd>
                            </dl>
                        </li>
                        <li data-name="salary-prediction" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="薪资预测">
                                <i class="layui-icon layui-icon-rmb"></i>
                                <cite>薪资预测</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd>
                                    <a lay-href="{% url "salary_prediction" %}">预测我的薪资</a>
                                </dd>
                            </dl>
                        </li>
                        <li data-name="set" class="layui-nav-item">
                            <a href="javascript:;" lay-tips="用户设置">
                                <i class="layui-icon layui-icon-set"></i>
                                <cite>用户设置</cite>
                            </a>
                            <dl class="layui-nav-child">
                                <dd>
                                    <a lay-href="{% url "pass_page" %}">修改密码</a>
                                </dd>
                            </dl>
                        </li>

                        <li data-name="get" class="layui-nav-item">
                            <a href="/admin" onclick="window.open(this.href); return false;" lay-tips="后台管理">
                                <i class="layui-icon layui-icon-headset"></i>
                                <cite>后台管理</cite>
                            </a>
                        </li>

                        <li data-name="get" class="layui-nav-item">
                            <a href="{% url "logout" %}" lay-tips="退出系统">
                                <i class="layui-icon layui-icon-auz"></i>
                                <cite>退出系统</cite>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 页面标签 -->
            <div class="layadmin-pagetabs" id="LAY_app_tabs">
                <div class="layui-icon layadmin-tabs-control layui-icon-prev" layadmin-event="leftPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-next" layadmin-event="rightPage"></div>
                <div class="layui-icon layadmin-tabs-control layui-icon-down">
                    <ul class="layui-nav layadmin-tabs-select" lay-filter="layadmin-pagetabs-nav">
                        <li class="layui-nav-item" lay-unselect>
                            <a href="javascript:;"></a>
                            <dl class="layui-nav-child layui-anim-fadein">
                                <dd layadmin-event="closeThisTabs"><a href="javascript:;">关闭当前标签页</a></dd>
                                <dd layadmin-event="closeOtherTabs"><a href="javascript:;">关闭其它标签页</a></dd>
                                <dd layadmin-event="closeAllTabs"><a href="javascript:;">关闭全部标签页</a></dd>
                            </dl>
                        </li>
                    </ul>
                </div>
                <div class="layui-tab" lay-unauto lay-allowClose="true" lay-filter="layadmin-layout-tabs">
                    <ul class="layui-tab-title" id="LAY_app_tabsheader">
                        <li lay-id="home/console.html" lay-attr="{% url "welcome" %}" class="layui-this"><i
                                class="layui-icon layui-icon-home"></i></li>
                    </ul>
                </div>
            </div>


            <!-- 主体内容 -->
            <div class="layui-body" id="LAY_app_body">
                <div class="layadmin-tabsbody-item layui-show">
                    <iframe src="{% url "welcome" %}" frameborder="0" class="layadmin-iframe"></iframe>
                </div>
            </div>

            <!-- 辅助元素，一般用于移动设备下遮罩 -->
            <div class="layadmin-body-shade" layadmin-event="shade"></div>
        </div>
    </div>
    </body>
{% endblock content %}
{% block js %}
    <script>
        layui.config({
            base: '{% static "layuiadmin/" %}' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use('index');
    </script>
{% endblock js %}


