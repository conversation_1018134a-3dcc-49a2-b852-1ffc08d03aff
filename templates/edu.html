

{% extends "base.html" %}
{% load static %}
{% block content %}
<body>

  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-sm12">
        <div class="layui-card">
          <div class="layui-card-header">学历要求分布-饼图</div>
          <div class="layui-card-body">
            <div class="layui-carousel layadmin-carousel layadmin-dataview" data-anim="fade" lay-filter="LAY-index-dataview">
              <div carousel-item id="LAY-index-dataview">
                <div><i class="layui-icon layui-icon-loading1 layadmin-loading"></i></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  </body>
{% endblock content %}
{% block js %}
  <script>
  layui.config({
    base: '{% static "layuiadmin/" %}' //静态资源所在路径
  }).extend({
    index: 'lib/index' //主入口模块
  }).use(['index', 'edu']);
  </script>
{% endblock js %}
