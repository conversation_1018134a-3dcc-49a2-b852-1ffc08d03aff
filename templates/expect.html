{% extends "base.html" %}
{% load static %}
{% block content %}
    <body>
    <div class="layui-fluid">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">求职意向设置</div>
                    <div class="layui-card-body">
                        <div class="layui-form" lay-filter="">
                            <div class="layui-form-item">
                                <label class="layui-form-label">期望职位</label>
                                <div class="layui-input-block">
                                    <input type="text" name="key_word" class="layui-input"
                                           placeholder="输入意向职位如：java" value="{{ keyword }}">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">城市选择</label>
                                <div class="layui-input-block">
                                    <select name="city" lay-verify="">
                                        {% if place == '' %}
                                            <option value="">请选择期望城市</option>
                                        {% else %}
                                            <option value="{{ place }}" class="layui-this">{{ place }}</option>
                                        {% endif %}
                                        <option value="北京">北京</option>
                                        <option value="上海">上海</option>
                                        <option value="天津">天津</option>
                                        <option value="重庆">重庆</option>
                                        <option value="广州">广州</option>
                                        <option value="深圳">深圳</option>
                                        <option value="苏州">苏州</option>
                                        <option value="南京">南京</option>
                                        <option value="杭州">杭州</option>
                                        <option value="大连">大连</option>
                                        <option value="成都">成都</option>
                                        <option value="武汉">武汉</option>
                                        <option value="西安">西安</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button class="layui-btn" lay-submit lay-filter="expect" id="expect">确认选择
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </body>
{% endblock %}

{% block js %}
    <script>
        layui.config({
            base: '{% static "layuiadmin/" %}' //静态资源所在路径
        }).extend({
            index: 'lib/index' //主入口模块
        }).use(['index', 'form'], function () {
            var $ = layui.$,
                layer = layui.layer
                , form = layui.form;

            form.on('submit(expect)', function (data) {
                var post_data = data.field;
                console.log(post_data)
                if (post_data.key_word == "") {
                    layer.msg("期望职位不能为空！")
                    return false
                }
                if (post_data.city == "") {
                    layer.msg("城市不能为空！")
                    return false
                }

                $.ajax({
                    type: 'POST',
                    url: '/job_expect/',
                    data: post_data,
                    success: function (res) {
                        layer.msg(res.msg);
                    },
                    error: function (response) {
                        layer.msg(response.msg);
                    }
                })

            });
        });
    </script>
{% endblock %}