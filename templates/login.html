{% load static %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>登入-职位推荐系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="{% static "layuiadmin/layui/css/layui.css" %}" media="all">
    <link rel="stylesheet" href="{% static "layuiadmin/style/admin.css" %}" media="all">
    <link rel="stylesheet" href="{% static "layuiadmin/style/login.css" %}" media="all">
    <!-- 引入自定义登录样式 -->
    <link rel="stylesheet" href="{% static "layuiadmin/style/custom-login.css" %}" media="all">
    <style>
        /* 覆盖任何可能的display:none样式 */
        #LAY-user-login {
            display: block !important;
        }
        
        /* 修复图标样式 */
        .login-icon {
            position: absolute;
            left: 15px;
            top: 12px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 18px;
            transition: all 0.3s;
            z-index: 1;
        }
        
        .layui-input:focus + .login-icon {
            color: #fff;
        }

        /* 使用Layui内置图标 */
        .layui-icon {
            font-size: 18px;
        }
    </style>
</head>
<body>

<div class="layadmin-user-login layadmin-user-display-show" id="LAY-user-login">
    <!-- 动画背景 -->
    <ul class="bg-bubbles">
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
        <li></li>
    </ul>
    
    <div class="layadmin-user-login-main">
        <div class="layadmin-user-login-box layadmin-user-login-header">
            <h2>职位推荐系统</h2>
            <p>探索你的职业未来，从这里开始</p>
        </div>
        <div class="layadmin-user-login-box layadmin-user-login-body layui-form">
            <div class="layui-form-item">
                <i class="layui-icon layui-icon-username login-icon"></i>
                <input type="text" name="user" placeholder="请输入账号" class="layui-input" autocomplete="off">
            </div>
            <div class="layui-form-item">
                <i class="layui-icon layui-icon-password login-icon"></i>
                <input type="password" name="password" placeholder="请输入密码" class="layui-input" autocomplete="off">
            </div>
            <div class="layui-form-item" style="margin-bottom: 20px;">
                <a href="{% url "register" %}" class="layadmin-user-jump-change layadmin-link" style="margin-top: 7px;">
                    <i class="layui-icon layui-icon-add-1" style="margin-right: 5px;"></i>还没账号？立即注册
                </a>
            </div>
            <div class="layui-form-item">
                <button class="layui-btn layui-btn-fluid" lay-submit lay-filter="LAY-user-login-submit">
                    <i class="layui-icon layui-icon-right" style="margin-right: 5px;"></i>登 录
                </button>
            </div>
        </div>
    </div>
    
    <div class="layadmin-user-login-footer">
        <p>© 2025 职位推荐系统 - 让求职更简单</p>
    </div>
</div>

<script src="{% static "layuiadmin/layui/layui.js" %}"></script>
<script>
    layui.config({
        base: '{% static "layuiadmin/" %}' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index', 'form', 'jquery'], function () {
        var $ = layui.jquery,
            form = layui.form,
            layer = layui.layer;

        // 添加输入框聚焦效果
        $('.layui-input').focus(function() {
            $(this).parent('.layui-form-item').addClass('layui-form-focus');
        }).blur(function() {
            $(this).parent('.layui-form-item').removeClass('layui-form-focus');
        });

        //提交
        form.on('submit(LAY-user-login-submit)', function (obj) {
            obj = obj.field;

            if (obj.user == '') {
                layer.msg('账号不能为空');
                return false;
            }
            if (obj.password == '') {
                layer.msg('密码不能为空');
                return false;
            }

            // 添加登录按钮加载效果
            var loadIndex = layer.load(2);
            
            //请求登入接口
            $.ajax({
                type: 'POST',
                url: '/login/',
                data: obj,
                success: function (res) {
                    layer.close(loadIndex);
                    // 弹窗内容， 样式
                    if (res.code == 0) {
                        layer.msg(res.msg + ' ' + res.user_name, {icon: 1, time: 1000});
                        setTimeout(function () {
                            location.href = "http://127.0.0.1:8000/index/";
                        }, 1000) // 延迟重定向
                    } else {
                        layer.msg(res.msg, {icon: 2});
                        return false;
                    }
                },
                error: function (response) {
                    layer.close(loadIndex);
                    layer.msg('登录失败，请稍后重试', {icon: 2});
                }
            })
        });
    });
</script>
</body>
</html>