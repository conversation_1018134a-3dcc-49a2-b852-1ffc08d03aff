# 基于Python招聘数据分析推荐系统架构图

## 系统概述

本系统是一个基于Django 3.2.11框架开发的招聘数据分析推荐系统，采用分层架构设计。系统通过协同过滤算法实现职位推荐，使用机器学习模型进行薪资预测，并提供数据可视化功能。数据库使用MySQL，前端使用LayUI框架和ECharts图表库。

## 系统架构图

```mermaid
graph TB
    %% 用户层
    subgraph "用户层 (User Layer)"
        U1[求职用户]
        U2[系统管理员]
    end

    %% 表现层 - 基于实际模板文件
    subgraph "表现层 (Presentation Layer)"
        subgraph "前端模板页面 (templates/)"
            P1["用户认证<br/>login.html/register.html"]
            P2["系统首页<br/>index.html/welcome.html"]
            P3["职位相关<br/>job_list.html/recommend.html<br/>favorite_jobs.html/send_list.html"]
            P4["薪资预测<br/>salary_prediction.html"]
            P5["数据可视化<br/>data_visualization.html<br/>可视化大屏.html"]
            P6["统计分析<br/>bar_page.html/edu.html<br/>salary.html/skill_heatmap.html"]
            P7["系统管理<br/>spiders.html/expect.html<br/>pass_page.html"]
        end

        subgraph "静态资源 (static/)"
            S1["前端框架<br/>layuiadmin/"]
            S2["图表库<br/>echarts.min.js"]
            S3["样式文件<br/>css/"]
            S4["脚本文件<br/>js/"]
            S5["图片资源<br/>images/背景.png"]
            S6["字体文件<br/>font/"]
            S7["城市数据<br/>city_data.json"]
        end
    end

    %% 控制层 - 基于实际URL配置
    subgraph "控制层 (Controller Layer)"
        subgraph "Django视图函数 (job/views.py)"
            V1["用户认证<br/>login/register/logout"]
            V2["系统首页<br/>index/welcome"]
            V3["职位管理<br/>job_list/get_job_list<br/>send_job/get_recommend"]
            V4["用户交互<br/>toggle_favorite/job_rate<br/>get_favorite_jobs"]
            V5["薪资预测<br/>predict_salary/train_salary_model<br/>get_model_info/get_available_models"]
            V6["数据可视化<br/>visualization_dashboard<br/>get_chart_data/get_skill_heatmap_data"]
            V7["统计分析<br/>get_pie/bar/salary/edu"]
            V8["爬虫控制<br/>spiders/start_spider"]
            V9["用户设置<br/>job_expect/up_info"]
        end

        subgraph "URL路由配置"
            R1["项目主路由<br/>JobRecommend/urls.py"]
            R2["应用子路由<br/>job/urls.py"]
        end
    end

    %% 业务逻辑层 - 基于实际算法文件
    subgraph "业务逻辑层 (Business Logic Layer)"
        subgraph "核心算法模块"
            B1["协同过滤推荐<br/>job/job_recommend.py"]
            B2["薪资预测<br/>job/salary_prediction.py"]
            B3["数据爬取<br/>job/tools.py"]
        end

        subgraph "具体算法实现"
            A1["相似度计算<br/>similarity()函数<br/>余弦相似度算法"]
            A2["基于物品协同过滤<br/>recommend_by_item_id()函数<br/>推荐算法核心"]
            A3["机器学习模型<br/>RandomForest/GradientBoosting<br/>Ridge/LinearRegression/LightGBM"]
            A4["特征工程<br/>extract_job_features()函数<br/>职位特征提取"]
            A5["网络爬虫<br/>lieSpider()函数<br/>Selenium自动化爬取"]
            A6["数据处理<br/>get_city_code/export_jobs_to_csv<br/>城市数据处理和导出"]
        end
    end

    %% 数据访问层 - 基于实际模型定义
    subgraph "数据访问层 (Data Access Layer)"
        subgraph "Django ORM模型 (job/models.py)"
            M1["用户模型<br/>UserList<br/>用户ID/用户名/密码"]
            M2["职位数据模型<br/>JobData<br/>职位信息/薪资/地点/学历/经验"]
            M3["用户职位交互模型<br/>UserJobInteraction<br/>评分/收藏状态/交互时间"]
            M4["投递记录模型<br/>SendList<br/>用户投递职位记录"]
            M5["薪资预测模型<br/>SalaryPredictionModel<br/>模型参数/性能指标"]
            M6["用户期望模型<br/>UserExpect<br/>期望职位/期望地点"]
            M7["爬虫信息模型<br/>SpiderInfo<br/>爬虫运行统计"]
            M8["用户薪资预测记录<br/>UserSalaryPrediction<br/>预测历史记录"]
        end
    end

    %% 数据层 - 基于实际数据库结构
    subgraph "数据层 (Data Layer)"
        subgraph "MySQL数据库 (recommend_job)"
            D1["用户表<br/>user_list<br/>主键:user_id"]
            D2["职位数据表<br/>job_data<br/>主键:job_id<br/>包含职位详细信息"]
            D3["用户职位交互表<br/>user_job_interaction<br/>主键:interaction_id<br/>外键:user_id,job_id"]
            D4["投递记录表<br/>send_list<br/>主键:send_id<br/>外键:user_id,job_id"]
            D5["薪资预测模型表<br/>salary_prediction_model<br/>主键:model_id<br/>存储序列化模型"]
            D6["用户期望表<br/>user_expect<br/>主键:expect_id<br/>外键:user_id"]
            D7["爬虫信息表<br/>spider_info<br/>主键:spider_id<br/>爬虫统计信息"]
            D8["用户薪资预测表<br/>user_salary_prediction<br/>主键:prediction_id<br/>预测历史"]
            D9["Django系统表<br/>auth_user/django_session<br/>认证和会话管理"]
        end

        subgraph "外部数据源"
            E1["猎聘网<br/>www.liepin.com<br/>职位数据来源"]
            E2["城市数据文件<br/>job/city_data.json<br/>static/city_data.json"]
            E3["导出文件<br/>job/exports/<br/>CSV格式职位数据"]
            E4["Chrome驱动<br/>job/chromedriver.exe<br/>爬虫浏览器驱动"]
        end
    end

    %% 外部服务层 - 基于实际依赖
    subgraph "外部服务层 (External Services)"
        EX1["Web浏览器<br/>Chrome + Selenium WebDriver<br/>自动化爬取"]
        EX2["机器学习库<br/>Scikit-learn<br/>模型训练和预测"]
        EX3["数据处理库<br/>Pandas/NumPy<br/>数据分析和处理"]
        EX4["Web框架<br/>Django 3.2.11<br/>后端框架"]
        EX5["数据库<br/>MySQL + PyMySQL<br/>数据存储"]
        EX6["前端库<br/>LayUI + ECharts<br/>UI和图表"]
    end

    %% 配置层 - 基于实际配置文件
    subgraph "配置层 (Configuration Layer)"
        C1["Django配置<br/>JobRecommend/settings.py<br/>数据库/静态文件/应用配置"]
        C2["WSGI配置<br/>JobRecommend/wsgi.py<br/>Web服务器接口"]
        C3["ASGI配置<br/>JobRecommend/asgi.py<br/>异步服务器接口"]
        C4["项目管理<br/>manage.py<br/>Django命令行工具"]
        C5["依赖配置<br/>requirements.txt<br/>Python包依赖"]
        C6["数据库脚本<br/>recommend_job.sql<br/>数据库初始化"]
        C7["模型训练脚本<br/>train_and_test_model.py<br/>独立训练脚本"]
    end

    %% 连接关系 - 基于实际数据流
    %% 用户层到表现层
    U1 --> P1
    U1 --> P2
    U1 --> P3
    U1 --> P4
    U1 --> P5
    U2 --> P6
    U2 --> P7

    %% 表现层到控制层
    P1 --> V1
    P2 --> V2
    P3 --> V3
    P3 --> V4
    P4 --> V5
    P5 --> V6
    P6 --> V7
    P7 --> V8
    P7 --> V9

    %% 控制层到业务逻辑层
    V1 --> M1
    V3 --> B1
    V4 --> M3
    V4 --> M4
    V5 --> B2
    V6 --> B2
    V7 --> M2
    V8 --> B3
    V9 --> M6

    %% 业务逻辑层内部连接
    B1 --> A1
    B1 --> A2
    B2 --> A3
    B2 --> A4
    B3 --> A5
    B3 --> A6

    %% 业务逻辑层到数据访问层
    B1 --> M2
    B1 --> M4
    B1 --> M6
    B2 --> M5
    B2 --> M8
    B3 --> M7

    %% 数据访问层到数据层
    M1 --> D1
    M2 --> D2
    M3 --> D3
    M4 --> D4
    M5 --> D5
    M6 --> D6
    M7 --> D7
    M8 --> D8

    %% 业务逻辑层到外部数据源
    B3 --> E1
    B3 --> E2
    B3 --> E3
    B3 --> E4

    %% 业务逻辑层到外部服务
    B2 --> EX2
    B2 --> EX3
    B3 --> EX1

    %% 配置层连接
    C1 --> EX4
    C1 --> EX5
    C4 --> EX4

    %% 样式定义
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef presentationLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef controllerLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef businessLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataAccessLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef externalLayer fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef configLayer fill:#fafafa,stroke:#424242,stroke-width:2px

    class U1,U2 userLayer
    class P1,P2,P3,P4,P5,P6,P7,S1,S2,S3,S4,S5,S6,S7 presentationLayer
    class V1,V2,V3,V4,V5,V6,V7,V8,V9,R1,R2 controllerLayer
    class B1,B2,B3,A1,A2,A3,A4,A5,A6 businessLayer
    class M1,M2,M3,M4,M5,M6,M7,M8 dataAccessLayer
    class D1,D2,D3,D4,D5,D6,D7,D8,D9,E1,E2,E3,E4 dataLayer
    class EX1,EX2,EX3,EX4,EX5,EX6 externalLayer
    class C1,C2,C3,C4,C5,C6,C7 configLayer
```

## 各层详细说明

### 1. 用户层 (User Layer)
- **求职用户**: 系统的主要使用者，可以注册登录、浏览职位、获取个性化推荐、预测薪资、收藏职位、投递简历
- **系统管理员**: 负责数据爬取控制、系统监控和数据可视化分析

### 2. 表现层 (Presentation Layer)
#### 前端模板页面 (templates/)
- **用户认证**: `login.html`、`register.html` - 用户登录注册界面
- **系统首页**: `index.html`、`welcome.html` - 系统主界面和欢迎页面
- **职位相关**: `job_list.html`、`recommend.html`、`favorite_jobs.html`、`send_list.html` - 职位浏览、推荐、收藏、投递管理
- **薪资预测**: `salary_prediction.html` - 薪资预测功能界面
- **数据可视化**: `data_visualization.html`、`可视化大屏.html` - 数据分析展示
- **统计分析**: `bar_page.html`、`edu.html`、`salary.html`、`skill_heatmap.html` - 各类统计图表
- **系统管理**: `spiders.html`、`expect.html`、`pass_page.html` - 爬虫控制、用户设置

#### 静态资源 (static/)
- **前端框架**: `layuiadmin/` - LayUI管理后台框架
- **图表库**: `echarts.min.js` - ECharts数据可视化库
- **样式文件**: `css/` - 自定义样式文件
- **脚本文件**: `js/` - JavaScript功能脚本
- **资源文件**: `images/`、`font/`、`city_data.json` - 图片、字体、城市数据

### 3. 控制层 (Controller Layer)
#### Django视图函数 (job/views.py) - 共71个URL路由
- **用户认证**: `login()`、`register()`、`logout()` - 用户登录注册登出
- **系统首页**: `index()`、`welcome()` - 首页和欢迎页面控制
- **职位管理**: `job_list()`、`get_job_list()`、`send_job()`、`get_recommend()` - 职位列表、投递、推荐
- **用户交互**: `toggle_favorite()`、`job_rate()`、`get_favorite_jobs()` - 收藏、评分、收藏列表
- **薪资预测**: `predict_salary()`、`train_salary_model()`、`get_model_info()`、`get_available_models()` - 薪资预测相关
- **数据可视化**: `visualization_dashboard()`、`get_chart_data()`、`get_skill_heatmap_data()` - 数据展示
- **统计分析**: `get_pie()`、`bar()`、`salary()`、`edu()` - 各类统计分析
- **爬虫控制**: `spiders()`、`start_spider()` - 爬虫管理
- **用户设置**: `job_expect()`、`up_info()` - 用户期望设置、信息修改

#### URL路由配置
- **项目主路由**: `JobRecommend/urls.py` - 包含71个路由规则
- **应用子路由**: `job/urls.py` - 应用级路由配置

### 4. 业务逻辑层 (Business Logic Layer)
#### 核心算法模块
- **协同过滤推荐**: `job/job_recommend.py` - 基于物品的协同过滤算法
- **薪资预测**: `job/salary_prediction.py` - 机器学习薪资预测模型
- **数据爬取**: `job/tools.py` - Selenium自动化数据爬取

#### 具体算法实现
- **相似度计算**: `similarity(job1_id, job2_id)` - 使用余弦相似度计算职位相似性
- **推荐算法**: `recommend_by_item_id(user_id, k=9)` - 基于用户历史行为的职位推荐
- **机器学习模型**: 支持RandomForest、GradientBoosting、Ridge、LinearRegression、LightGBM等多种模型
- **特征工程**: `extract_job_features()` - 职位特征提取和预处理
- **网络爬虫**: `lieSpider(key_word, city, all_page)` - 从猎聘网爬取职位数据
- **数据处理**: 城市代码处理、CSV导出、数据清洗等功能

### 5. 数据访问层 (Data Access Layer)
#### Django ORM模型 (job/models.py)
- **UserList**: 用户基本信息模型 - 用户ID、用户名、密码
- **JobData**: 职位数据模型 - 职位名称、薪资、地点、学历、经验、公司信息等
- **UserJobInteraction**: 用户职位交互模型 - 评分、收藏状态、交互时间
- **SendList**: 投递记录模型 - 用户投递职位的记录
- **SalaryPredictionModel**: 薪资预测模型 - 存储训练好的机器学习模型参数
- **UserExpect**: 用户期望模型 - 用户期望职位和工作地点
- **SpiderInfo**: 爬虫信息模型 - 爬虫运行统计信息
- **UserSalaryPrediction**: 用户薪资预测记录 - 用户的薪资预测历史

### 6. 数据层 (Data Layer)
#### MySQL数据库 (recommend_job)
- **用户表 (user_list)**: 主键user_id，存储用户基本信息
- **职位数据表 (job_data)**: 主键job_id，存储从猎聘网爬取的职位详细信息
- **用户职位交互表 (user_job_interaction)**: 记录用户对职位的评分和收藏状态
- **投递记录表 (send_list)**: 记录用户投递简历的历史
- **薪资预测模型表 (salary_prediction_model)**: 存储序列化的机器学习模型
- **用户期望表 (user_expect)**: 存储用户的求职意向
- **爬虫信息表 (spider_info)**: 记录爬虫运行统计数据
- **用户薪资预测表 (user_salary_prediction)**: 存储用户的薪资预测历史记录
- **Django系统表**: auth_user、django_session等系统表

#### 外部数据源
- **猎聘网 (www.liepin.com)**: 主要的职位数据来源
- **城市数据文件**: job/city_data.json 和 static/city_data.json
- **导出文件**: job/exports/ 目录下的CSV格式数据文件
- **Chrome驱动**: job/chromedriver.exe 用于Selenium自动化

### 7. 外部服务层 (External Services)
- **Web浏览器**: Chrome + Selenium WebDriver 用于自动化数据爬取
- **机器学习库**: Scikit-learn 提供各种回归算法支持
- **数据处理库**: Pandas、NumPy 用于数据分析和处理
- **Web框架**: Django 3.2.11 作为后端开发框架
- **数据库**: MySQL + PyMySQL 用于数据存储和访问
- **前端库**: LayUI + ECharts 提供UI组件和数据可视化

### 8. 配置层 (Configuration Layer)
- **Django配置 (JobRecommend/settings.py)**: 数据库连接、静态文件、应用配置、SimpleUI配置
- **WSGI配置 (JobRecommend/wsgi.py)**: Web服务器网关接口配置
- **ASGI配置 (JobRecommend/asgi.py)**: 异步服务器网关接口配置
- **项目管理 (manage.py)**: Django命令行管理工具
- **依赖配置 (requirements.txt)**: Python包依赖列表
- **数据库脚本 (recommend_job.sql)**: 完整的数据库初始化脚本
- **模型训练脚本 (train_and_test_model.py)**: 独立的薪资预测模型训练脚本

## 系统特点

1. **真实项目架构**: 基于实际运行的Django项目，包含完整的功能模块
2. **分层设计清晰**: 严格按照MVC架构模式，各层职责明确
3. **算法驱动**: 核心推荐功能基于协同过滤算法，薪资预测基于机器学习
4. **数据完整性**: 包含完整的数据库设计，支持用户行为跟踪和分析
5. **可视化丰富**: 提供多种数据可视化图表和大屏展示
6. **爬虫集成**: 集成Selenium爬虫，可实时获取最新职位数据
7. **用户体验**: 支持职位收藏、评分、个性化推荐等交互功能

## 技术栈详情

- **后端框架**: Django 3.2.11 + SimpleUI管理界面
- **前端技术**: HTML5, CSS3, JavaScript, LayUI框架, ECharts图表库
- **数据库**: MySQL 5.7+ + PyMySQL连接器
- **机器学习**: Scikit-learn (RandomForest, GradientBoosting, Ridge, LinearRegression, LightGBM)
- **数据处理**: Pandas, NumPy, 正则表达式
- **爬虫技术**: Selenium WebDriver + Chrome浏览器
- **开发语言**: Python 3.9.10
- **部署配置**: WSGI/ASGI支持，支持生产环境部署
