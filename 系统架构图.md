# 基于Python招聘数据分析推荐系统架构图

## 系统概述

本系统是一个基于Django框架开发的招聘数据分析推荐系统，采用分层架构设计，主要包括用户层、表现层、控制层、业务逻辑层、数据访问层和数据层。系统通过协同过滤算法实现职位推荐，使用机器学习模型进行薪资预测，并提供数据可视化功能。

## 系统架构图

```mermaid
graph TB
    %% 用户层
    subgraph "用户层 (User Layer)"
        U1[求职用户]
        U2[系统管理员]
    end

    %% 表现层
    subgraph "表现层 (Presentation Layer)"
        subgraph "前端页面"
            P1[登录/注册页面<br/>login.html/register.html]
            P2[首页<br/>index.html]
            P3[职位推荐页面<br/>recommend.html]
            P4[职位列表页面<br/>job_list.html]
            P5[薪资预测页面<br/>salary_prediction.html]
            P6[数据可视化页面<br/>data_visualization.html]
            P7[收藏职位页面<br/>favorite_jobs.html]
            P8[爬虫控制页面<br/>spiders.html]
            P9[可视化大屏<br/>可视化大屏.html]
        end
        
        subgraph "静态资源"
            S1[CSS样式文件]
            S2[JavaScript脚本]
            S3[ECharts图表库]
            S4[LayUI框架]
            S5[图片资源]
        end
    end

    %% 控制层
    subgraph "控制层 (Controller Layer)"
        subgraph "Django视图 (views.py)"
            V1[用户认证视图<br/>login/register/logout]
            V2[职位推荐视图<br/>get_recommend]
            V3[薪资预测视图<br/>predict_salary]
            V4[数据可视化视图<br/>visualization_dashboard]
            V5[职位管理视图<br/>send_job/toggle_favorite]
            V6[爬虫控制视图<br/>start_spider]
            V7[数据统计视图<br/>get_chart_data]
        end
        
        subgraph "URL路由 (urls.py)"
            R1[项目路由配置<br/>JobRecommend/urls.py]
            R2[应用路由配置<br/>job/urls.py]
        end
    end

    %% 业务逻辑层
    subgraph "业务逻辑层 (Business Logic Layer)"
        subgraph "核心算法模块"
            B1[协同过滤推荐算法<br/>job_recommend.py]
            B2[薪资预测算法<br/>salary_prediction.py]
            B3[数据爬取模块<br/>tools.py]
        end
        
        subgraph "算法实现"
            A1[相似度计算<br/>similarity函数]
            A2[基于物品的协同过滤<br/>recommend_by_item_id]
            A3[机器学习模型<br/>随机森林/梯度提升/岭回归]
            A4[特征工程<br/>extract_job_features]
            A5[Selenium爬虫<br/>lieSpider函数]
        end
    end

    %% 数据访问层
    subgraph "数据访问层 (Data Access Layer)"
        subgraph "Django ORM"
            M1[用户模型<br/>UserList]
            M2[职位数据模型<br/>JobData]
            M3[用户职位交互模型<br/>UserJobInteraction]
            M4[投递记录模型<br/>SendList]
            M5[薪资预测模型<br/>SalaryPredictionModel]
            M6[用户期望模型<br/>UserExpect]
            M7[爬虫信息模型<br/>SpiderInfo]
        end
    end

    %% 数据层
    subgraph "数据层 (Data Layer)"
        subgraph "MySQL数据库"
            D1[用户表<br/>user_list]
            D2[职位数据表<br/>job_data]
            D3[用户职位交互表<br/>user_job_interaction]
            D4[投递记录表<br/>send_list]
            D5[薪资预测模型表<br/>salary_prediction_model]
            D6[用户期望表<br/>user_expect]
            D7[爬虫信息表<br/>spider_info]
        end
        
        subgraph "外部数据源"
            E1[猎聘网<br/>www.liepin.com]
            E2[城市数据<br/>city_data.json]
            E3[导出文件<br/>CSV格式]
        end
    end

    %% 外部服务层
    subgraph "外部服务层 (External Services)"
        EX1[Chrome浏览器<br/>Selenium WebDriver]
        EX2[机器学习库<br/>Scikit-learn]
        EX3[数据处理库<br/>Pandas/NumPy]
    end

    %% 连接关系
    U1 --> P1
    U1 --> P2
    U1 --> P3
    U1 --> P4
    U1 --> P5
    U1 --> P6
    U1 --> P7
    U2 --> P8
    U2 --> P9

    P1 --> V1
    P2 --> V2
    P3 --> V2
    P4 --> V5
    P5 --> V3
    P6 --> V6
    P7 --> V5
    P8 --> V6
    P9 --> V4

    V1 --> M1
    V2 --> B1
    V3 --> B2
    V4 --> V7
    V5 --> M2
    V5 --> M3
    V5 --> M4
    V6 --> B3
    V7 --> M2

    B1 --> A1
    B1 --> A2
    B1 --> M2
    B1 --> M4
    B1 --> M6

    B2 --> A3
    B2 --> A4
    B2 --> M5
    B2 --> EX2

    B3 --> A5
    B3 --> EX1
    B3 --> E1

    M1 --> D1
    M2 --> D2
    M3 --> D3
    M4 --> D4
    M5 --> D5
    M6 --> D6
    M7 --> D7

    B3 --> E2
    B3 --> E3

    A3 --> EX3

    %% 样式定义
    classDef userLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef presentationLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef controllerLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef businessLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef dataAccessLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef externalLayer fill:#e0f2f1,stroke:#004d40,stroke-width:2px

    class U1,U2 userLayer
    class P1,P2,P3,P4,P5,P6,P7,P8,P9,S1,S2,S3,S4,S5 presentationLayer
    class V1,V2,V3,V4,V5,V6,V7,R1,R2 controllerLayer
    class B1,B2,B3,A1,A2,A3,A4,A5 businessLayer
    class M1,M2,M3,M4,M5,M6,M7 dataAccessLayer
    class D1,D2,D3,D4,D5,D6,D7,E1,E2,E3 dataLayer
    class EX1,EX2,EX3 externalLayer
```

## 各层详细说明

### 1. 用户层 (User Layer)
- **求职用户**: 系统的主要使用者，可以浏览职位、获取推荐、预测薪资等
- **系统管理员**: 负责数据爬取、系统监控和数据可视化分析

### 2. 表现层 (Presentation Layer)
#### 前端页面模块
- **用户认证页面**: 提供用户登录和注册功能
- **职位相关页面**: 包括职位列表、推荐页面、收藏页面等
- **功能页面**: 薪资预测、数据可视化、爬虫控制等
- **管理页面**: 可视化大屏，用于数据展示和分析

#### 静态资源
- **前端框架**: LayUI提供UI组件支持
- **图表库**: ECharts实现数据可视化
- **样式和脚本**: CSS和JavaScript文件

### 3. 控制层 (Controller Layer)
#### Django视图函数
- **用户认证控制**: 处理登录、注册、登出逻辑
- **业务功能控制**: 职位推荐、薪资预测、数据统计等
- **数据管理控制**: 职位收藏、投递、爬虫启动等

#### URL路由配置
- **项目级路由**: 总体路由分发
- **应用级路由**: 具体功能路由映射

### 4. 业务逻辑层 (Business Logic Layer)
#### 核心算法模块
- **协同过滤推荐**: 基于用户行为和职位相似度的推荐算法
- **薪资预测**: 使用机器学习模型预测职位薪资范围
- **数据爬取**: 从外部招聘网站获取职位数据

#### 算法实现细节
- **相似度计算**: 使用余弦相似度计算职位间相似性
- **推荐算法**: 基于物品的协同过滤算法实现
- **机器学习**: 支持多种回归模型（随机森林、梯度提升等）
- **特征工程**: 职位特征提取和预处理
- **网络爬虫**: 使用Selenium自动化爬取数据

### 5. 数据访问层 (Data Access Layer)
#### Django ORM模型
- **用户相关**: 用户信息、用户期望职位
- **职位相关**: 职位数据、用户职位交互、投递记录
- **系统相关**: 薪资预测模型、爬虫信息

### 6. 数据层 (Data Layer)
#### MySQL数据库
- **用户数据**: 存储用户账户和偏好信息
- **职位数据**: 存储从外部网站爬取的职位信息
- **交互数据**: 记录用户与职位的交互行为
- **模型数据**: 存储训练好的机器学习模型

#### 外部数据源
- **招聘网站**: 猎聘网作为主要数据来源
- **配置文件**: 城市数据等配置信息
- **导出文件**: 支持CSV格式数据导出

### 7. 外部服务层 (External Services)
- **浏览器驱动**: Chrome WebDriver用于网页自动化
- **机器学习库**: Scikit-learn提供算法支持
- **数据处理库**: Pandas和NumPy用于数据处理和分析

## 系统特点

1. **分层架构**: 采用经典的MVC分层架构，职责分离清晰
2. **算法驱动**: 核心功能基于协同过滤和机器学习算法
3. **数据驱动**: 通过爬虫获取实时数据，支持数据可视化分析
4. **用户体验**: 提供个性化推荐和薪资预测功能
5. **可扩展性**: 模块化设计，便于功能扩展和维护

## 技术栈

- **后端框架**: Django
- **前端技术**: HTML, CSS, JavaScript, LayUI, ECharts
- **数据库**: MySQL
- **机器学习**: Scikit-learn, Pandas, NumPy
- **爬虫技术**: Selenium, BeautifulSoup
- **开发语言**: Python
