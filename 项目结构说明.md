# JobRecommend 项目结构说明

## 项目概述

JobRecommend是一个基于协同过滤的就业推荐系统，基于Django框架开发。系统主要功能包括职位信息展示、职位推荐、薪资预测、数据可视化等。系统通过协同过滤算法，根据用户历史行为（如投递简历、收藏职位）和职位特征，为用户推荐合适的职位信息。

## 目录结构

### 根目录

```
JobRecommend/
├── templates/              # 前端模板目录
├── .idea/                  # IDE配置目录
├── static/                 # 静态资源目录
├── job/                    # 主应用模块
├── venv/                   # 虚拟环境
├── JobRecommend/           # 项目配置模块
├── train_and_test_model.py # 薪资预测模型训练与测试脚本
├── requirements.txt        # 项目依赖包列表
├── recommend_job.sql       # 数据库SQL文件
├── manage.py               # Django项目管理脚本
└── python-3.9.10-amd64.exe # Python安装包
```

### 主应用模块 (job/)

```
job/
├── __pycache__/            # Python缓存文件
├── views.py                # 视图函数，处理HTTP请求
├── models/                 # 数据模型扩展目录
├── exports/                # 导出数据目录
├── tools.py                # 工具函数集合
├── city_data.json          # 城市数据文件
├── after_click_other.png   # 图片资源
├── salary_prediction.py    # 薪资预测模块
├── migrations/             # 数据库迁移文件
├── urls.py                 # URL路由配置
├── admin.py                # 管理后台配置
├── apps.py                 # 应用配置
├── models.py               # 数据模型定义
├── job_recommend.py        # 职位推荐核心算法
├── chromedriver.exe        # 爬虫Selenium驱动
└── __init__.py             # 包初始化文件
```

### 项目配置模块 (JobRecommend/)

```
JobRecommend/
├── __pycache__/            # Python缓存文件
├── settings.py             # 项目设置
├── urls.py                 # 项目URL路由配置
├── __init__.py             # 包初始化文件
├── wsgi.py                 # WSGI服务器配置
└── asgi.py                 # ASGI服务器配置
```

### 前端模板 (templates/)

```
templates/
├── login.html              # 登录页面
├── register.html           # 注册页面
├── 可视化大屏.html          # 可视化大屏页面
├── welcome.html            # 欢迎页面
├── skill_heatmap.html      # 技能热图页面
├── index.html              # 首页
├── spiders.html            # 爬虫控制页面
├── salary_prediction.html  # 薪资预测页面
├── recommend.html          # 职位推荐页面
├── error.html              # 错误页面
├── favorite_jobs.html      # 收藏职位页面
├── job_list.html           # 职位列表页面
├── data_visualization.html # 数据可视化页面
├── pass_page.html          # 通过页面
├── expect.html             # 期望职位页面
├── base.html               # 基础模板
├── bar_page.html           # 柱状图页面
├── edu.html                # 教育统计页面
├── salary.html             # 薪资统计页面
├── send_list.html          # 投递列表页面
└── map.html                # 地图页面
```

### 静态资源 (static/)

```
static/
├── 背景.png                 # 背景图片
├── js/                     # JavaScript文件目录
├── css/                    # CSS样式文件目录
├── font/                   # 字体文件目录
├── images/                 # 图片资源目录
├── city_data.json          # 城市数据JSON文件
├── echarts.min.js          # ECharts可视化库
└── layuiadmin/             # Layui后台框架资源
```

## 核心功能代码位置

### 职位推荐功能
- **文件路径**: `job/job_recommend.py`
- **核心方法**:
  - `similarity(job1_id, job2_id)`: 计算两个职位之间的相似度，使用余弦相似度算法
  - `recommend_by_item_id(user_id, k=9)`: 基于物品的协同过滤推荐算法，为用户推荐职位

### 薪资预测功能
- **文件路径**: 
  - `job/salary_prediction.py`: 薪资预测实现
  - `train_and_test_model.py`: 薪资预测模型训练与测试
- **核心方法**:
  - `SalaryPredictor.train_model()`: 训练薪资预测模型
  - `SalaryPredictor.predict_salary()`: 预测职位薪资
  - `extract_job_features()`: 提取职位特征用于预测

### 数据抓取功能
- **文件路径**: `job/tools.py`
- **核心方法**:
  - `lieSpider(key_word, city, all_page)`: 爬虫实现，从猎聘网获取职位数据

### 用户交互与视图处理
- **文件路径**: `job/views.py`
- **核心方法**:
  - `login(request)`: 用户登录处理
  - `register(request)`: 用户注册处理
  - `get_recommend(request)`: 获取职位推荐
  - `predict_salary(request)`: 处理薪资预测请求
  - `toggle_favorite(request)`: 切换职位收藏状态
  - `get_favorite_jobs(request)`: 获取用户收藏的职位
  - `visualization_dashboard(request)`: 数据可视化大屏

### 数据库模型
- **文件路径**: `job/models.py`
- **主要模型**:
  - `JobData`: 职位信息数据模型
  - `UserList`: 用户信息数据模型
  - `UserJobInteraction`: 用户职位交互数据模型
  - `SalaryPredictionModel`: 薪资预测模型数据模型
  - `UserSalaryPrediction`: 用户薪资预测记录数据模型
  - `UserExpect`: 用户期望职位数据模型

### 项目配置
- **文件路径**: 
  - `JobRecommend/settings.py`: Django项目配置
  - `JobRecommend/urls.py`: 项目URL路由配置

## 系统主要功能模块

### 1. 用户认证模块
- **相关文件**: `job/views.py`中的`login()`, `register()`, `logout()`函数
- **功能描述**: 用户注册、登录和登出

### 2. 职位推荐模块
- **相关文件**: `job/job_recommend.py`, `job/views.py`中的`get_recommend()`函数
- **功能描述**: 通过协同过滤算法，基于用户历史行为和职位特征，为用户推荐合适的职位

### 3. 薪资预测模块
- **相关文件**: `job/salary_prediction.py`, `job/views.py`中的`predict_salary()`等函数
- **功能描述**: 使用机器学习模型，预测职位薪资范围，支持多种模型（随机森林、梯度提升、岭回归等）

### 4. 职位管理模块
- **相关文件**: `job/views.py`中的`send_job()`, `toggle_favorite()`等函数
- **功能描述**: 用户投递简历、收藏职位等操作

### 5. 数据可视化模块
- **相关文件**: `job/views.py`中的`visualization_dashboard()`, `get_chart_data()`等函数
- **功能描述**: 通过图表展示职位数据分布、薪资趋势、技能热度等信息

### 6. 数据采集模块
- **相关文件**: `job/tools.py`中的`lieSpider()`函数, `job/views.py`中的`start_spider()`函数
- **功能描述**: 通过爬虫从招聘网站采集职位数据

## 技术栈

- **后端框架**: Django
- **前端技术**: HTML, CSS, JavaScript, LayUI, ECharts
- **数据库**: MySQL
- **机器学习**: Scikit-learn, Pandas, Numpy
- **爬虫技术**: Selenium, BeautifulSoup 